export { default as <PERSON><PERSON>yPaymentMethod } from '@magento2/pages/payments/methods/AlipayPaymentMethod';
export { default as CreditCardPaymentMethod } from '@magento2/pages/payments/methods/CreditCardPaymentMethod';
export { default as MultibancoPaymentMethod } from '@magento2/pages/payments/methods/MultibancoPaymentMethod';
export { default as BillinkPaymentMethod } from '@magento2/pages/payments/methods/BillinkPaymentMethod';
export { default as GiftCardPaymentMethod } from '@magento2/pages/payments/methods/GiftCardPaymentMethod';
export { default as IdealPaymentMethod } from '@magento2/pages/payments/methods/IdealPaymentMethod';
export { default as In3PaymentMethod } from '@magento2/pages/payments/methods/In3PaymentMethod';
export { default as KlarnaPaymentMethod } from '@magento2/pages/payments/methods/KlarnaPaymentMethod';
export { default as KlarnaPayPaymentMethod } from '@magento2/pages/payments/methods/KlarnaPayPaymentMethod';
export { default as KlarnaSliceItPaymentMethod } from '@magento2/pages/payments/methods/KlarnaSliceItPaymentMethod';
export { default as PayByBankPaymentMethod } from '@magento2/pages/payments/methods/PayByBankPaymentMethod';
export { default as PaypalPaymentMethod } from '@magento2/pages/payments/methods/PaypalPaymentMethod';
export { default as MbwayPaymentMethod } from '@magento2/pages/payments/methods/MbwayPaymentMethod';
export { default as KbcPaymentMethod } from '@magento2/pages/payments/methods/KbcPaymentMethod';
export { default as BelfiusPaymentMethod } from '@magento2/pages/payments/methods/BelfiusPaymentMethod';
export { default as RivertyPaymentMethod } from '@magento2/pages/payments/methods/RivertyPaymentMethod';
export { default as WeChatPayPaymentMethod } from '@magento2/pages/payments/methods/WeChatPayPaymentMethod';
export { default as TrustlyPaymentMethod } from '@magento2/pages/payments/methods/TrustlyPaymentMethod';
export { default as TransferPaymentMethod } from '@magento2/pages/payments/methods/TransferPaymentMethod';
export { default as SepaPaymentMethod } from '@magento2/pages/payments/methods/SepaPaymentMethod';
export { default as BlikPaymentMethod } from '@magento2/pages/payments/methods/BlikPaymentMethod';
export { default as Przelewy24PaymentMethod } from '@magento2/pages/payments/methods/Przelewy24PaymentMethod';
export { default as PayPerEmailPaymentMethod } from '@magento2/pages/payments/methods/PayPerEmailPaymentMethod';
