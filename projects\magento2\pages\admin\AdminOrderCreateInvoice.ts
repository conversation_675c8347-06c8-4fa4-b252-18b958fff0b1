import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminOrderDetailsPage } from '@magento2/pages/admin/index';
import OrderSummaryService from '@core/services/workflow/OrderSummary.service';
import { expect } from '@playwright/test';
import moment from 'moment';
import { first, isNil } from 'lodash-es';

export default class AdminOrderCreateInvoice extends AdminOrderDetailsPage {
    protected newInvoiceDate: moment.Moment;

    constructor() {
        super();
        this.newInvoiceDate = moment().subtract(10, 'seconds');
    }

    /*
     * Navigates to the invoices tab.
     */
    protected async createInvoice() {
        await this.navigateToInvoices();

        await magentoPlugin.helper.clickAndExpectVisible('admin.orderDetailed.invoices.newInvoiceButton', 'New Invoice button not found.');
        await magentoPlugin.helper.waitForVisibleAndClick('admin.orderDetailed.invoices.submitInvoiceButton', 'Submit Invoice button not found.');

        await this.findOrderByID();
    }

    /*
     * Verifies that the new shipment was created successfully
     */
    protected async verifyNewShipment() {
        const capturedTransaction = first(
            await this.adminOrderHistoryService.fetchTransactions({
                hasBckTransactionKey: true,
                isCapture: true,
                fromDate: this.newInvoiceDate,
            })
        );
        const orderSummary = await new OrderSummaryService(magentoPlugin.getSelector('admin.orderDetailed.orderSummary')).parse();
        const expectedPaidAmount = magentoPlugin.sharedData['orderSummary']['orderTotal'];

        await this.verifyOrderStatus('Processing');
        expect(isNil(capturedTransaction), 'No captured transaction found').toBe(false);
        expect(capturedTransaction?.status).toBe('Processing');
        expect(orderSummary.paidAmount).toBe(expectedPaidAmount);

        console.log(`Order paid total: ${orderSummary.paidAmount} - Expected: ${expectedPaidAmount}`);
    }

    /*
     * Creates a new invoice for the current order.
     */
    public async createShipmentInvoice(): Promise<void> {
        console.log(`Creating new invoice for order ${this.orderNumber} at ${this.newInvoiceDate.format('YYYY-MM-DD HH:mm:ss')}`);

        if (!this.orderNumber) return Promise.reject('Order number not found');

        if (!(await this.isInsideDetailsPage())) {
            await this.setOrderNumber(this.orderNumber).findOrderByID();
        }

        await this.createInvoice();

        await this.verifyNewShipment();
    }
}
