import BasePage from '@core/pages/BasePage';
import { PlazaService } from '@core/services/buckaroo';
import { PlazaOrderDetailsPage } from '@core/pages/plaza/index';
import { globalContext } from '@core/context/TestContext';
import { expect } from '@core/fixtures/BaseTest';

export default class PlazaPerformScenarioPage extends BasePage {
    protected plazaService: PlazaService;
    protected plazaOrderDetailsPage!: PlazaOrderDetailsPage;

    constructor() {
        super();
        this.plazaService = new PlazaService();
        this.plazaOrderDetailsPage = new PlazaOrderDetailsPage();
    }

    /**
     * Processes a refund by performing necessary admin actions.
     */
    async processPerformScenario(scenario: string): Promise<void> {
        const orderNumber = await this.plazaOrderDetailsPage.getOrderNumber();
        if (!orderNumber) {
            throw new Error('Order number could not be retrieved.');
        }

        globalContext.sharedData['orderNumber'] = orderNumber;
        console.log(`Order Number: ${orderNumber}`);

        await globalContext.page.locator(this.plazaTestScenarioSelectors.actionsButton).click();
        await globalContext.page.locator(this.plazaTestScenarioSelectors.testScenarioLink).click();

        const liLocator = globalContext.page.locator(globalContext.getSelector('plaza.testScenario.findScenario')(scenario));
        const scenarioAnchor = liLocator.locator(`> a`);
        await scenarioAnchor.click();

        const accordionContent = liLocator.locator('div.k-content');
        await accordionContent.waitFor({ state: 'visible' });

        const performScenarioButton = accordionContent.locator(globalContext.helper.resolveLocator('plaza.testScenario.performScenarioButton'));
        await performScenarioButton.click();
    }

    get plazaTestScenarioSelectors() {
        return globalContext.getSelector('plaza.testScenario');
    }

    /**
     * Verifies the refund success on the user side.
     */
    public async verifyPerformedScenario(): Promise<void> {
        await globalContext.page.locator(this.plazaTestScenarioSelectors.successMessage).waitFor();

        expect(
            await globalContext.helper.getText(this.plazaTestScenarioSelectors.successMessage, {
                errorMessage: 'Transaction Successful message does not exists',
                strict: true,
            }),
            `The success message was not displayed after performing scenario on Plaza.`
        ).toBe('Successfully performed scenario');
    }
}
