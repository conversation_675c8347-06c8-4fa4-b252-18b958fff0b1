import { ProcessFlowService } from '@core/services/workflow/index';
import { PaymentMethodCode } from '@/utils/paymentMethods';

export interface Hooks<TProcessFlowService> {
    before?: (processFlowService: TProcessFlowService) => Promise<void>;
    after?: (processFlowService: TProcessFlowService) => Promise<void>;
    beforeNegative?: (processFlowService: TProcessFlowService) => Promise<void>;
    afterNegative?: (processFlowService: TProcessFlowService) => Promise<void>;
}

export interface PaymentGatewayOptions {
    paymentFee?: number | string;

    [key: string]: any;
}

export interface ExecuteStepsParams<TStepName extends string, TOptions = PaymentGatewayOptions> {
    paymentMethod?: PaymentMethodCode;
    paymentGatewayOptions?: TOptions;
    hooks?: Partial<Record<TStepName, Hooks<ProcessFlowService>>>;
    disabledSteps?: TStepName[];
}

export interface Step<TStepName extends string> {
    name: TStepName;
    group?: TStepName;
    action: () => Promise<void>;
}
