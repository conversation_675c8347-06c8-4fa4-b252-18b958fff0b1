import { wooApiService } from '@woo/services/api/index';

/**
 * Types for Buckaroo settings
 */
export interface BuckarooMethodSettings {
    enabled?: 'yes' | 'no';
    description?: string;
    title?: string;

    [key: string]: any;
}

/**
 * Service class for managing Buckaroo payment settings
 */
export default class BuckarooSettingsApiService {
    /**
     * Get Buckaroo settings for specific payment keys or master settings
     * @param keys Single method name, comma-separated method names, or 'mastersettings'
     * @returns Promise with settings data
     */
    async getSettings(keys: string = 'mastersettings') {
        console.log(`Getting Buckaroo settings for: ${keys}`);

        return await wooApiService().post('/actions/action/settings_get', {
            keys,
        });
    }

    /**
     * Update Buckaroo settings for one or more payment keys
     * @param method
     * @param settings Object with method names as keys and their settings as values
     * @returns Promise with update response
     */
    async updateSettings(method: string = 'mastersettings', settings: BuckarooMethodSettings) {
        console.log(`Updating Buckaroo settings for: ${Object.keys(settings).join(', ')}`);

        return await wooApiService().post('/actions/action/settings_update', {
            [method]: settings,
        });
    }
}
