import AdminPage from '@magento2/pages/admin/AdminPage';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class AdminConfigPage extends AdminPage {
    /**
     * Update currency configuration.
     * @param currency - Currency code (e.g., 'USD').
     */
    public async updateCurrency(currency: string): Promise<void> {
        console.log(`Updating currency: ${currency}`);
        await this.loginToAdmin();
        await this.navigateToStoreConfigs();

        await this.setConfigValues([
            {
                key: '#currency_options_base',
                value: currency,
                method: 'selectOption',
            },
        ]);
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
    }

    /**
     * Update invoice handling configuration.
     * @param value
     */
    public async updateInvoiceHandling(value: 'shipment' | 'payment'): Promise<void> {
        console.log(`Updating "Invoice Handling" to: ${value}`);
        await this.loginToAdmin();
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([
            { section: this.adminSelectors.tabSales },
            { hrefPart: '/admin/system_config/edit/section/buckaroo_magento2' },
        ]);

        await this.setConfigValues([
            {
                key: '#buckaroo_magento2_buckaroo_magento2_account_section_invoice_handling',
                value: value == 'payment' ? '1' : '2',
                method: 'selectOption',
            },
        ]);
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
        magentoPlugin.sharedData['invoice_handling'] = value;
    }

    /**
     * Add Free Shipping Method if not already enabled.
     */
    public async addFreeShippingMethod(): Promise<void> {
        console.log(`Adding 'Free Shipping' Method..`);
        await this.loginToAdmin();
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([{ section: this.adminSelectors.tabSales }, { hrefPart: '/admin/system_config/edit/section/carriers' }]);
        await this.openSection('Free Shipping');
        await magentoPlugin.page.locator(this.adminSelectors.config.inheritFreeShipping).uncheck();
        await magentoPlugin.page.locator(this.adminSelectors.config.freeShipping).selectOption({ label: 'Yes' });
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
        console.log(`'Free Shipping' Method added successfully.`);
    }

    /**
     * Enable Admin Account Sharing and Set Admin Session lifetime to maximum
     */
    public async enableAdminAccountSharingAndSessionTime(): Promise<void> {
        const maxAllowdSessionTime = '3153600';
        console.log(`Setting Admin Accoung Sharing to YES, and Admin session lifetime to maximum hours`);

        await this.loginToAdmin();
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([{ section: this.adminSelectors.tabAdvanced }, { hrefPart: '/admin/system_config/edit/section/admin' }]);
        await this.openSection('Security');
        await magentoPlugin.page.locator(this.adminSelectors.config.inheritAdminAccountSharing).uncheck();
        await magentoPlugin.page.locator(this.adminSelectors.config.adminAccountSharing).selectOption({ label: 'Yes' });
        await magentoPlugin.page.locator(this.adminSelectors.config.inheritSessionLifetime).uncheck();
        await magentoPlugin.page.locator(this.adminSelectors.config.adminSessionLifetime).fill(maxAllowdSessionTime);
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
        console.log(`'Admin Account Sharing' is set to 'Yes' and 'Admin Session Lifetime' is set to maximum hours (${maxAllowdSessionTime}).`);
    }

    /**
     * Set Backorders to allow Qty below 0.
     * This allows products to be ordered even if they are out of stock.
     */
    public async allowBackOrders(): Promise<void> {
        console.log(`Allowing Backorders..`);
        await this.loginToAdmin();
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([
            { section: this.adminSelectors.tabCatalog },
            { hrefPart: '/admin/system_config/edit/section/cataloginventory' },
        ]);
        await this.openSection('Product Stock Options');
        await magentoPlugin.page.locator(this.adminSelectors.config.inheritManageStock).uncheck();
        await magentoPlugin.page.locator(this.adminSelectors.config.manageStock).selectOption({ label: 'No' });
        console.log(`Manage Stock is set to 'No'`);

        await magentoPlugin.page.locator(this.adminSelectors.config.inheritBackorders).uncheck();
        await magentoPlugin.page.locator(this.adminSelectors.config.backorders).selectOption({ label: 'Allow Qty Below 0' });
        console.log(`Backorders is set to 'Allow Qty Below 0'`);

        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
    }

    /**
     * Update the Timezone setting in Magento Admin
     * @param timezone - The timezone to set (e.g., 'Europe/Tirane').
     */
    public async setTimeZone(timezone: string): Promise<void> {
        console.log(`Setting Timezone to ${timezone}`);
        await this.loginToAdmin();
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([{ hrefPart: '/admin/system_config/edit/section/general' }]);
        await this.openSection('Locale Options');
        await this.setConfigValues([
            {
                key: '#general_locale_timezone',
                value: timezone,
                method: 'selectOption',
            },
        ]);
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
    }

    /**
     * Open a specific section in the configuration page if it is not already open.
     * @param sectionName - The name of the section to open.
     */
    public async openSection(sectionName: string): Promise<void> {
        const locator = magentoPlugin.page.locator(`.entry-edit-head.admin__collapsible-block a:has-text('${sectionName}')`);
        if (!(await locator.evaluate(el => el.classList.contains('open')))) {
            await locator.click();
        }
    }
}
