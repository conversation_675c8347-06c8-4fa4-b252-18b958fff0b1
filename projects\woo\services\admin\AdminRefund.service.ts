import { Step } from '@core/services/workflow/ProcessFlow.types';
import { ProcessFlowService } from 'core/services/workflow';
import { RefundExecuteStepsParams, RefundPlacementPages, RefundSteps } from '@woo/services/admin';
import wooPlugin from '@woo/WooPlugin';
import { AuthApiService } from '@woo/services/api';
import { AdminOrderDetailsPage } from '@woo/pages/admin';

export default class AdminRefundService extends ProcessFlowService<RefundSteps, RefundPlacementPages, RefundExecuteStepsParams> {
    constructor() {
        super();

        this.pages.adminOrderDetailsPage = new AdminOrderDetailsPage();
    }

    /**
     * Defines the sequence of steps for refunding from Plaza.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<RefundSteps>[] {
        return [
            {
                name: RefundSteps.StorefrontLogin,
                action: async () => {
                    await new AuthApiService().login();
                },
            },
            {
                name: RefundSteps.GoToOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.setOrderNumber(wooPlugin.sharedData['orderNumber']).navigateHere();
                },
            },
            {
                name: RefundSteps.ProcessRefund,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.processRefund(this.options?.partial);
                },
            },
            {
                name: RefundSteps.VerifyRefund,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.verifyRefund();
                },
            },
        ];
    }
}
