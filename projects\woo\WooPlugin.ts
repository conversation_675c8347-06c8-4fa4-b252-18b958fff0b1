import { Stage, WooSelectorsMapType } from '@woo/selectors';
import { TestContext } from '@core/context/TestContext';
import { SelectorsMapType } from '@core/context/TestContext.types';
import selectors_base from '@woo/selectors/selectors_base';
import { PaymentFactory } from '@woo/services/payments';

export class WooPlugin extends TestContext<WooSelectorsMapType[Stage]> {
    public static selectorsMap: SelectorsMapType = {
        base: selectors_base,
    };

    /**
     * Returns the PaymentFactory instance.
     */
    get paymentFactory() {
        return PaymentFactory;
    }
}

const wooPlugin = new WooPlugin();

export default wooPlugin;
