import { wooApiService } from '@woo/services/api/index';
import { chain } from 'lodash-es';
import wooPlugin from '@woo/WooPlugin';
import { Product } from '@woo/services/storefront/Order.types';

/**
 * Service class handling all product-related API operations
 */
export default class ProductApiService {
    /**
     * Adds multiple items to the cart based on their IDs and quantities.
     * @param products - An array of products with their IDs and quantities.
     */
    async addItemsToCart(products: Product[] = []): Promise<void> {
        if (products.length) {
            for (const product of products) {
                await this.addItemToCart(product);
            }
        } else {
            await this.addItemToCart();
        }
    }

    /**
     * Adds a single item to the cart
     * @param product - The product details
     * @returns Promise resolving to the response data
     */
    async addItemToCart(product?: Partial<Product>): Promise<any> {
        const randomProduct = this.getRandomProductId();
        const { id = randomProduct, quantity = 1 } = product || {};

        const response = await wooApiService().post('/actions/action/wc_add_to_cart', {
            product_id: id,
            quantity,
        });

        if (response.success) {
            console.log(`Added ${quantity} item(s) '${response.data.name}' to your shopping cart.`);
            return response.data;
        }

        throw new Error(`Failed to add item to cart: ${response.message}`);
    }

    /**
     * Retrieves the ID of a random product from the homepage
     * @returns The ID of a random product
     */
    public getRandomProductId(): string {
        return chain(wooPlugin.getSelector('productValues.id')).values().sample().value();
    }
}
