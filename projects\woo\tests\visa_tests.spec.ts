import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('VISA Tests', () => {
    test('Place order with VISA', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'visa' },
        });
    });

    test('Place order with VISA (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'visa' },
        });
    });

    test('Refund order with VISA (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'visa' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with VISA (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'visa' },
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with VISA (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'visa' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with VISA (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcard', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15, card: 'visa' },
        });
    });

    test('Place order with VISA (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcards', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'visa' },
        });
    });

    test('Place order with VISA (NO)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'visa', responseStatus: 'N' },
        });
    });

    test('Place order with VISA (Unknown)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'visa', responseStatus: 'U' },
        });
    });
});
