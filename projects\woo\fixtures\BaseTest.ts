import { expect, test as base } from '@core/fixtures/BaseTest';
import { OrderService } from '@woo/services/storefront';
import { CustomFixtures } from '@woo/fixtures/types';
import { AdminPlazaRefundService, AdminRefundService } from '@woo/services/admin';

const test = base.extend<CustomFixtures>({
    orderService: async ({}, use): Promise<void> => {
        await use(new OrderService());
    },
    refundService: async ({}, use): Promise<void> => {
        await use(new AdminRefundService());
    },
    plazaRefundService: async ({}, use): Promise<void> => {
        await use(new AdminPlazaRefundService());
    },
});

export { test, expect };
