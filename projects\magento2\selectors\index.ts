import selectors_m245 from '@magento2/selectors/selectors_m245';
import selectors_m246 from '@magento2/selectors/selectors_m246';
import selectors_m247 from '@magento2/selectors/selectors_m247';
import selectors_refactor from '@magento2/selectors/selectors_refactor';
import { SelectorsMapType } from '@core/selectors';

export interface MagentoSelectorsMapType {
    m245: typeof selectors_m245 & SelectorsMapType;
    m246: typeof selectors_m246 & SelectorsMapType;
    m247: typeof selectors_m247 & SelectorsMapType;
    refactor: typeof selectors_refactor & SelectorsMapType;
}

export type Stage = keyof MagentoSelectorsMapType;
