import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('American Express Tests', () => {
    test('Place order with American Express', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'amex' },
        });
    });

    test('Place order with American Express (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
        });
    });

    test('Refund order with American Express (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with American Express (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with American Express (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'amex' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with American Express (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcard', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15, card: 'amex' },
        });
    });

    test('Place order with American Express (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcards', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'amex' },
        });
    });

    test('Place order with American Express (NO)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'amex', responseStatus: 'N' },
        });
    });

    test('Place order with American Express (Unknown)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'amex', responseStatus: 'U' },
        });
    });
});
