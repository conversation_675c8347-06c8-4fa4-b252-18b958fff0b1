import { wooApiService } from '@woo/services/api/index';
import wooPlugin from '@woo/WooPlugin';
import { env } from '@/utils/env';

/**
 * Service class for handling authentication related operations
 */
export default class AuthApiService {
    /**
     * Logs out the user from the storefront.
     */
    async logout(): Promise<void> {
        return await wooApiService().post('/auth/logout');
    }

    /**
     * Logs in the user with the provided credentials.
     */
    async login(): Promise<void> {
        if (wooPlugin.sharedData['isLoggedInStorefront']) {
            console.log('User is already logged in.');
            return;
        }

        console.log('Signing in...');

        const response = await wooApiService().post('auth/login', {
            username: env('USER_EMAIL'),
            password: env('PASSWORD'),
        });

        if (response.success) {
            console.log('User signed in successfully with email: ' + env('USER_EMAIL'));
            wooPlugin.sharedData['isLoggedInStorefront'] = true;
            return;
        }

        console.log('User sign in failed: ' + response.message);
    }
}
