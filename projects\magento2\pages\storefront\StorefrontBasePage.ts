import BasePage from '@core/pages/BasePage';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminOrderService } from '@magento2/services/admin';

export default class StorefrontBasePage extends BasePage {
    protected readonly adminOrderService: AdminOrderService;

    constructor() {
        super();
        this.adminOrderService = new AdminOrderService();
    }

    /**
     * Verifies if already logged in.
     */
    public async isAlreadyLoggedIn(): Promise<boolean> {
        return Boolean(magentoPlugin.sharedData['isLoggedInStorefront']);
    }
}
