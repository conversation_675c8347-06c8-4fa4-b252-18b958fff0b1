import { PlazaService } from '@core/services/buckaroo';
import { PlazaLoginPage } from '@core/pages/plaza/index';
import BasePage from '@core/pages/BasePage';
import { env } from '@/utils/env';
import { expect } from '@playwright/test';
import { globalContext } from '@core/context/TestContext';

export default class PlazaWebsiteSettingsPage extends BasePage {
    protected plazaService: PlazaService;
    protected plazaLoginPage: PlazaLoginPage;

    constructor() {
        super();
        this.plazaService = new PlazaService();
        this.plazaLoginPage = new PlazaLoginPage();
    }

    /**
     * Selects the push content type from the dropdown in the website settings.
     * @param contentType - The content type to select.
     */
    public async selectPushContentType(contentType: string): Promise<void> {
        console.log(`Logging in to Plaza.`);
        await this.plazaLoginPage.login();

        console.log(`Navigating to Plaza website settings.`);
        await this.plazaService.navigateToWebsiteSettings();

        console.log(`Selecting Website on filter`);
        await this.selectWebsiteOnFilter();

        console.log('Selecting Push content type: ' + contentType);
        await globalContext.page.getByRole('tab', { name: 'Push settings' }).click();
        await globalContext.page.locator(globalContext.getSelector('plaza.pushContentTypeBtn')).selectOption({ value: contentType });
        await globalContext.page.getByRole('button', { name: 'Save' }).click();
        console.log(`Saving Push content type.`);

        const successPopup = globalContext.page.locator(globalContext.getSelector('plaza.savedSuccessMessage'));

        await expect(successPopup, 'After selecting the Push content type on Plaza, the save confirmation is missing').toContainText(
            'Save successful',
            { ignoreCase: true }
        );

        console.log('Push content type selected successfully. ');
    }

    /**
     * Selects the website on the filter based on the provided stage.
     */
    async selectWebsiteOnFilter(): Promise<void> {
        const key: string = env('WEBSITE_KEY');

        if (!key) {
            throw new Error('WEBSITE_KEY is not defined in the environment variables.');
        }

        const filterSelector = globalContext.page.locator(globalContext.getSelector('plaza.selectWebsiteDropdown'));

        await filterSelector.selectOption({ value: key });

        const selectedLabel = await filterSelector.locator('option:checked').textContent();

        console.log(`Store key: ${key}, Currently selected website:` + selectedLabel);
    }
}
