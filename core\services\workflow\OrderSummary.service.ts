import { get, isNil, keys, set, zipObject } from 'lodash-es';
import { globalContext } from '@core/context/TestContext';

export default class OrderSummaryService<T extends Record<string, any>> {
    constructor(
        protected selectors: Record<keyof T, string>,
        protected fields?: (keyof T)[]
    ) {}

    async parse(): Promise<T> {
        if (!this.fields) {
            this.fields = keys(this.selectors) as (keyof T)[];
        }

        const fields = this.fields.map((field: keyof T) => ({
            key: field,
            selector: get(this.selectors, field),
        }));

        const orderSummary = zipObject<T>(this.fields, Array(this.fields.length).fill(null));

        for (const field of fields) {
            const valueStr = await globalContext.helper.getText(field.selector as string, { strict: false }).catch(() => null);

            if (valueStr) {
                const value = await globalContext.helper.moneyParser().parse(valueStr);

                if (isNil(value)) {
                    console.error(`Error parsing value for field '${String(field.key)}':`, valueStr);
                    continue;
                }

                if (isNil(orderSummary['currency'])) {
                    set(orderSummary, 'currency', value.currencyCode);
                }

                set(orderSummary, field.key, value.floatValue);
            }
        }

        console.log('Order Summary: ', orderSummary);
        return orderSummary as T;
    }
}
