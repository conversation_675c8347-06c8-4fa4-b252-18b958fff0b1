import { globalContext } from '@core/context/TestContext';

export default class TransactionStatusService {
    protected bckTransactionKey: string;

    constructor(bckTransactionKey: string) {
        this.bckTransactionKey = bckTransactionKey;

        if (!globalContext.buckarooClient.confirmCredentials()) {
            throw new Error('Buckaroo credentials are not valid.');
        }
    }

    async status() {
        return await globalContext.buckarooClient.transaction(this.bckTransactionKey).status();
    }
}
