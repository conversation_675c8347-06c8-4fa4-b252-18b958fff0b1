import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('Bancontact Tests', () => {
    const bancontactPaymentFee = magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.BANCONTACT);

    test('Place order with Bancontact', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.BANCONTACT, [
            {
                key: '#payment_us_buckaroo_magento2_payment_section_buckaroo_magento2_mrcash_buckaroo_magento2_advanced_card_design',
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
        });
    });

    test('Refund order with Bancontact (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Bancontact (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'bancontact' },
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Bancontact (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'bancontact' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Bancontact (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.BANCONTACT, [
            {
                key: bancontactPaymentFee,
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15, card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.BANCONTACT, [
            {
                key: bancontactPaymentFee,
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'bancontact' },
        });
    });

    test('Place order with Bancontact (NO)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact', responseStatus: 'N' },
        });
    });

    test('Place order with Bancontact (Unknown)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BANCONTACT,
            paymentGatewayOptions: { card: 'bancontact', responseStatus: 'U' },
        });
    });
});
