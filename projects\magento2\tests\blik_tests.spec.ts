import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { AdminBuckarooConfigPage, AdminConfigPage } from '@magento2/pages/admin';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';

test.describe('Blik Tests', () => {
    test('Place order with Blik', async ({ orderService }) => {
        await new AdminConfigPage().updateCurrency('PLN');

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'flat',
        });
    });

    test('Place order with B<PERSON> (as guest)', async ({ orderService }) => {
        await new AdminConfigPage().updateCurrency('PLN');

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with B<PERSON> (from Admin)', async ({ orderService, refundService }) => {
        await new AdminConfigPage().updateCurrency('PLN');

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Blik (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Blik (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await new AdminConfigPage().updateCurrency('PLN');

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Blik (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('blik', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.BLIK),
                value: '15',
                method: 'fill',
            },
        ]);
        await new AdminConfigPage().updateCurrency('PLN');
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Blik (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('blik', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.BLIK),
                value: '15%',
                method: 'fill',
            },
        ]);
        await new AdminConfigPage().updateCurrency('PLN');
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BLIK,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
