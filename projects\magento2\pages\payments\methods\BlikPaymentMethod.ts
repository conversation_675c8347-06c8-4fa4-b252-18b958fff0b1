import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class BlikPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.completeWalletSteps();
    }

    /**
     * Completes the wallet steps specific to Blik.
     */
    protected async completeWalletSteps(): Promise<void> {
        console.log(`Completing wallet step: Next`);
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.walletMethods.nextButton'));
        await this.checkoutPage.completeWalletSteps();
        console.log('Blik wallet steps completed.');
    }
}
