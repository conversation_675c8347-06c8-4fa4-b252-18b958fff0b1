import { IdealPaymentMethod as BaseIdealPaymentMethod } from '@core/pages/payments/methods';
import { OrderSteps } from '@magento2/services/storefront';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';

export default class IdealPaymentMethod extends BaseIdealPaymentMethod {
    /**
     Executes the steps to handle unsuccessful order placements.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);

        await this.submitStatus(this.options.responseStatus);

        await expect(wooPlugin.page).toHaveURL(/\/checkout\/\?bck_err=.+/);
        console.log('Order succeeded as expected');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        console.log('Submitting status...');
        await this.submitStatus();
    }
}
