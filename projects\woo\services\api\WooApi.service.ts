import { Page } from '@playwright/test';
import { env } from '@/utils/env';
import wooPlugin from '@woo/WooPlugin';
import { RequestData, RequestOptions, Response } from './WooApi.service.types';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * Service class for handling Wordpress API requests
 * Provides a clean interface to interact with Buckaroo endpoints
 */
export class WooApiService {
    private readonly baseUrl: string;
    private readonly apiKey: string;
    private readonly requestContext: Page['request'];

    /**
     * Initialize the Buckaroo API service
     * @param page Playwright page instance
     */
    constructor(page: Page) {
        this.requestContext = page.request;
        this.baseUrl = new URL('/wp-json/buckaroo-qa/v1', env('BASE_URL')).toString().replace(/\/$/, '');
        this.apiKey = env('WOO_BUCKAROO_API_KEY');
    }

    /**
     * Private base method to make requests to the Buckaroo API
     * @param endpoint API endpoint path
     * @param method HTTP method
     * @param data Request data (body for POST/PUT/PATCH/DELETE, query params for GET)
     * @returns Promise with JSON response
     */
    private async request<T = Response>({ endpoint, method, data = {} }: RequestOptions): Promise<T> {
        const url = this.composeUrl(endpoint, method, data);

        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            'X-Buckaroo-Api-Key': this.apiKey,
        };

        if (wooPlugin.sharedData['x-wc-session']) {
            headers['X-WC-Session'] = wooPlugin.sharedData['x-wc-session'];
        }

        const response = await this.requestContext.fetch(url, {
            method,
            headers,
            data: method === 'GET' ? undefined : data,
        });

        const newSession = response.headers()['x-wc-session'];
        if (newSession) {
            wooPlugin.sharedData['x-wc-session'] = newSession;
        }

        if (!response.ok()) {
            const errText = await response.text();
            console.error(`Buckaroo API error (${response.status()}): ${errText}`);
            throw new Error(errText);
        }

        return (await response.json()) as T;
    }

    /**
     * Make a GET request to the Buckaroo API
     * @param endpoint API endpoint path
     * @param queryParams Query parameters
     * @returns Promise with JSON response
     */
    async get<T = Response>(endpoint: string, queryParams: RequestData = {}): Promise<T> {
        return this.request<T>({ endpoint, method: 'GET', data: queryParams });
    }

    /**
     * Make a POST request to the Buckaroo API
     * @param endpoint API endpoint path
     * @param data Request body data
     * @returns Promise with JSON response
     */
    async post<T = Response>(endpoint: string, data: RequestData = {}): Promise<T> {
        return this.request<T>({ endpoint, method: 'POST', data });
    }

    /**
     * Make a PUT request to the Buckaroo API
     * @param endpoint API endpoint path
     * @param data Request body data
     * @returns Promise with JSON response
     */
    async put<T = Response>(endpoint: string, data: RequestData = {}): Promise<T> {
        return this.request<T>({ endpoint, method: 'PUT', data });
    }

    /**
     * Make a PATCH request to the Buckaroo API
     * @param endpoint API endpoint path
     * @param data Request body data
     * @returns Promise with JSON response
     */
    async patch<T = Response>(endpoint: string, data: RequestData = {}): Promise<T> {
        return this.request<T>({ endpoint, method: 'PATCH', data });
    }

    /**
     * Make a DELETE request to the Buckaroo API
     * @param endpoint API endpoint path
     * @param data Optional request body data
     * @returns Promise with JSON response
     */
    async delete<T = Response>(endpoint: string, data: RequestData = {}): Promise<T> {
        return this.request<T>({ endpoint, method: 'DELETE', data });
    }

    /**
     * Converts endpoint + data into an absolute URL, appending query
     * parameters only for GET requests.
     */
    private composeUrl(endpoint: string, method: HttpMethod, data: RequestData): string {
        const url = new URL(endpoint.replace(/^\//, ''), `${this.baseUrl}/`);

        if (method === 'GET' && data) {
            const params = new URLSearchParams();

            // copy only defined / non-null values
            Object.entries(data).forEach(([k, v]) => {
                if (v != null) params.append(k, String(v));
            });

            if (params.toString()) url.search = params.toString();
        }

        return url.toString();
    }
}

const wooApiService = () => new WooApiService(wooPlugin.page);

export default wooApiService;
