import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class PayByBankPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after selecting the payment method.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        await magentoPlugin.page.isVisible('text=Select a bank:');
        await this.moreBanksBtn();
        await this.selectBank();
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.confirmPayment();
    }

    /**
     * CLicks on More banks button to display all banks
     */

    async moreBanksBtn(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.payByBank.moreBanks'));
    }

    /**
     * Selects a bank from the available options.
     */
    protected async selectBank(): Promise<void> {
        const bankLocator = magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.payByBank.ingBank'));

        await bankLocator.locator('..').click();
        console.log('ING bank selected');
    }

    /**
     * Confirms the payment by clicking the confirmation button.
     */
    protected async confirmPayment(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.payByBank.okButtonIngBank'));
        console.log('Payment confirmed');
    }
}
