import { PaymentMethod } from '@core/services/payments';
import wooPlugin from '@woo/WooPlugin';

export default class MultibancoPaymentMethod extends PaymentMethod {
    /**
     * Proceeds through the "Next Step" process a specified number of times.
     * @param steps - The number of times to click the "Next Step" button.
     */
    protected async proceedThroughNextSteps(steps: number): Promise<void> {
        for (let i = 0; i < steps; i++) {
            await wooPlugin.page.click(wooPlugin.getSelector('storefront.checkout.nextStepWallet'));
            await wooPlugin.page.waitForLoadState('networkidle');
        }
    }
}
