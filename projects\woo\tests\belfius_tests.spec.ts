import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('Belfius Tests', () => {
    test('Place order with Belfius', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
        });
    });

    test('Place order with Belfius (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Place order with Belfius (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with Belfius (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Belfius (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Belfius (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Belfius (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_belfius', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: '15' },
        });
    });

    test('Place order with Belfius (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_belfius', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with Belfius (Failed)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            paymentGatewayOptions: { responseStatus: '490' },
        });
    });

    test('Place order with Belfius (Canceled)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });

    test('Place order and Refund with Belfius with content type "json"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with Belfius with content type "httppost"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
        });

        await refundService.executeSteps();
    });
});
