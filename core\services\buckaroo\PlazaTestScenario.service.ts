import { Step } from '@core/services/workflow/ProcessFlow.types';
import { PlazaLoginPage, PlazaOrderDetailsPage, PlazaPerformScenarioPage } from 'core/pages/plaza';
import { ProcessFlowService } from '@core/services/workflow';
import { PlazaTestScenarioExecuteStepsParams, PlazaTestScenarioPlacementPages, PlazaTestScenarioSteps } from '@core/services/buckaroo/Plaza.types';
import { globalContext } from '@core/context/TestContext';

export default class PlazaTestScenarioService extends ProcessFlowService<
    PlazaTestScenarioSteps,
    PlazaTestScenarioPlacementPages,
    PlazaTestScenarioExecuteStepsParams
> {
    constructor() {
        super();

        this.pages.plazaLoginPage = new PlazaLoginPage();
        this.pages.plazaOrderDetailsPage = new PlazaOrderDetailsPage();
        this.pages.plazaPerformScenarioPage = new PlazaPerformScenarioPage();
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<PlazaTestScenarioSteps>[] {
        return [
            {
                name: PlazaTestScenarioSteps.PlazaLogin,
                action: async () => {
                    await this.pages.plazaLoginPage.login();
                },
            },
            {
                name: PlazaTestScenarioSteps.GoToTransactionPage,
                action: async () => {
                    await this.pages.plazaOrderDetailsPage.navigateToTransaction(globalContext.sharedData['bckTransactionKey']);
                },
            },
            {
                name: PlazaTestScenarioSteps.ProcessPerformScenario,
                action: async () => {
                    await this.pages.plazaPerformScenarioPage.processPerformScenario(this.options.scenario);
                },
            },
            {
                name: PlazaTestScenarioSteps.VerifyPerformedScenario,
                action: async () => {
                    await this.pages.plazaPerformScenarioPage.verifyPerformedScenario();
                },
            },
        ];
    }
}
