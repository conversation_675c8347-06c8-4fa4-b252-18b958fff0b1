/**
 * Defines the different stages or environments the tests can run in.
 * This is a generic type that platforms can override with their specific stage values.
 */
export type Stage = keyof SelectorsMapType;

/**
 * Options for getting selectors.
 */
export interface GetSelectorOptions {
    throwOnMissing?: boolean;
    errorMessage?: string;
    showWarns?: boolean;
}

/**
 * Generates all possible dot-separated keys from a nested object type `T`.
 * For example, given T = { a: string; b: { c: string; d: { e: string; }; }; }
 * The resulting type would be: 'a' | 'b' | 'b.c' | 'b.d' | 'b.d.e'
 */
export type DotNotationKeys<T> = T extends object
    ? {
          [K in Extract<keyof T, string>]: K | (T[K] extends object ? `${K}.${DotNotationKeys<T[K]>}` : never);
      }[Extract<keyof T, string>]
    : never;

export interface SelectorsMapType {
    [key: string]: Record<any, any>;
}

/**
 * Generic type for platform-specific selector keys
 */
export type SelectorKey = DotNotationKeys<SelectorsMapType[Stage]>;
