import { PaymentMethod } from '@magento2/services/payments';

export default class WeChatPayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.completeWalletSteps();
    }

    /**
     * Completes the wallet steps specific to WeChatPay.
     */
    protected async completeWalletSteps(): Promise<void> {
        await this.checkoutPage.completeWalletSteps();
        console.log('WeChatPay wallet steps completed.');
    }
}
