export default {
    productValues: {
        // ============================
        // Multiple Product Selectors
        // ============================
        id: {
            heroHoodie: '158',
            breatheEasy: '1812',
        },
        colors: {
            black: '49',
            gray: '52',
            white: '59',
        },
        sizes: {
            s: '167',
            m: '168',
        },
    },
    storefront: {
        product: {
            itemQuantity: "input[name='qty']",
            selectSizeOptions: 'div.swatch-attribute[data-attribute-code="size"] div.swatch-option',
            selectSizeOption: (optionId: string) =>
                `div.swatch-attribute[data-attribute-code="size"] div.swatch-option[data-option-id="${optionId}"]`,

            selectColorOptions: 'div.swatch-attribute[data-attribute-code="color"] div.swatch-option',
            selectColorOption: (optionId: string) =>
                `div.swatch-attribute[data-attribute-code="color"] div.swatch-option[data-option-id="${optionId}"]`,
            addToCart: 'button.action.tocart.primary',
            goToCart: '.action.showcart',
            proceedToCheckout: '.action.primary.checkout',
            itemTitle: '[data-ui-id="page-title-wrapper"]',

            firstProductItem: 'li.product-item:first-child',
        },
        checkout: {
            freeShippingMethod: '[value="freeshipping_freeshipping"]',
            flatShippingMethod: '[value="flatrate_flatrate"]',
            reviewAndPayments: 'button[data-role="opc-continue"]',
            submitStatus: 'input[value="Submit status"]',
            selectStatus: 'select[name="sc"]',
            nextStepWallet: '.btn.btn-primary',
            selectPaymentMethod: (paymentMethod: string) => `.buckaroo_magento2_${paymentMethod} [name="payment[method]"]`,
            paymentMethodTitle: 'div.step-title:has-text("Payment Method")',
            placeOrderButton: "button[id*='button-action-checkout']:not(.disabled):visible",
            itemAddedSuccessMessage: 'div.message-success',
            paymentErrorMessage: 'div.message-error',
            selectGender: '[id$="genderSelect"]:visible',
            dateOfBirth: '[id$="_DoB"]:visible',
            phoneInput: '[id$="_Telephone"]:visible',
            clickAway: "//div[@class='opc-wrapper']",
            customerForm: {
                email: '#customer-email',
                firstName: 'input[name="firstname"]',
                lastName: 'input[name="lastname"]',
                company: 'input[name="company"]',
                street1: 'input[name="street[0]"]',
                street2: 'input[name="street[1]"]',
                country: 'select[name="country_id"]',
                state: 'input[name="region"]',
                city: 'input[name="city"]',
                zip: 'input[name="postcode"]',
                phone: 'input[name="telephone"]',
            },
            orderSummary: {
                blockSummary: '.opc-block-summary:not(._block-content-loading)',
                buckarooPaidLabel: '.opc-block-summary table tbody tr.totals.buckaroo_already_paid > th.mark > span',
                getPaidWithText: (cardName: string) => new RegExp(`paid with ${cardName}`, 'i'),

                prices: {
                    subtotal: '.opc-block-summary table tbody tr.totals.sub td.amount .price',
                    discount: '.opc-block-summary table tbody tr.totals.discount td.amount .price',
                    buckarooPaidAmount: '.opc-block-summary table tbody tr.totals.buckaroo_already_paid td.amount',
                    alreadyPaid: '.opc-block-summary table tbody tr.totals.already-paid td.amount .price',
                    shippingFee: '.opc-block-summary table tbody tr.totals.shipping.incl td.amount .price',
                    orderTotal: '.opc-block-summary table tbody tr.grand.totals td.amount .price',
                    remainingAmount: '.opc-block-summary table tbody tr.grand.totals.remaining-amount td.amount .price',
                    paidAmount: '.opc-block-summary table tbody tr.totals.paid-amount td.amount .price',
                    buckarooFee: '.opc-block-summary table tbody tr.totals.buckaroo_fee td.amount',
                    totalTax: '.opc-block-summary table tbody tr.totals-tax td.amount .price',
                },
            },
        },
        successfulPage: {
            orderNumber: 'a.order-number strong',
            orderNumberAsGuest: 'div.checkout-success p span',
            orderConfirmationDetails: 'div.checkout-success',
        },

        auth: {
            usernameInput: 'input[name="login[username]"]',
            passwordInput: 'input[name="login[password]"]',
            submitButton: '.action.login.primary',
        },
    },

    paymentMethods: {
        ideal: {
            bankTypes: '.buckaroo_magento2_ideal .bank-types',
            selectINGbank: 'input[value="INGBNL2A"]',
        },
        payPal: {
            continueToReviewOrder: '#payment-submit-btn',
            email: 'input[name="login_email"]',
            password: 'input[name="login_password"]',
            loginBtn: 'button[name="btnLogin"]',
            clickNext: 'button[name="btnNext"]',
        },
        billink: {
            sameAsShippingCheckbox: '#billing-address-same-as-shipping-buckaroo_magento2_billink',
            cocInput: '#buckaroo_magento2_billink_chamberOfCommerce',
            vatInput: '#buckaroo_magento2_billink_VATNumber',
        },
        klarnaPay: {
            phone: '#phonePasskey',
            continue: '#onContinue__text',
            enterCode: '#otp_field',
            buyButton: 'button[id^="buy_button"]',
        },
        klarnaSliceIt: {
            billingSameAsShippingCheckbox: '#billing-address-same-as-shipping-buckaroo_magento2_klarnain',
            continueBtn: "button[data-testid='pick-plan']",
            chooseHowToPayView: '#stacked-selection-title',
            iFrame: 'iframe[title="Open banking form"]',
            continueBtnIframe: 'button:has-text("Continue")',
            usernameIframe: 'input[aria-label="Username"]',
            passwordIframe: 'input[aria-label="Password"]',
            otpIframe: 'input[aria-label="OTP"]',
            firstName: '[name="firstname"]',
            lastName: '[name="lastname"]',
            address: '[name="street[0]"]',
            country: '[name="country_id"]',
            city: '[name="city"]',
            postcode: '[name="postcode"]',
            phoneNr: '[name="telephone"]',
            saveAddressCheckbox: '#shipping-save-in-address-book',
            shipHereBtn: '.action.primary.action-save-address',
            newAddressBtn: '.action.action-show-popup',
        },
        in3: {
            checkConditions: '#chk-conditions',
            orderNow: '#btn-continue',
            finalizeOrder: '#btn-continue',
            returnToWebshop: '#btn-cancel',
        },
        payByBank: {
            moreBanks: '.bk-toggle-text',
            ingBank: '.buckaroo_magento2_paybybank input[value="INGBNL2A"]',
            okButtonIngBank: '.MuiButton-label',
        },
        creditCards: {
            nameOnCard: 'input[placeholder="Name on card"]',
            cardNumber: 'input[placeholder="Card number"]',
            cardExpMonth: 'input[placeholder="MM"]',
            cardExpYear: 'input[placeholder="YY"]',
            cardCVV: 'input[id*="CVV"]',
            payNowBtn: 'div.button-text:text("Pay Now")',
            paymentStatusDropdown: "select[name='sc']",
            clickAwayCart: 'body',
        },
        sepa: {
            bankAccountHolder: '#bankaccountholder',
            bankAccountNumber: '#bankaccountnumber',
            bicNumberInput: '#bicnumber',
        },
        giftCards: {
            buckarooCheckoutMethod: `.payment-method.page-link[data-method="$paymentMethod"]`,
            nrRedirect: `[id^="brq_SERVICE_$giftcardName_"][placeholder="Card number"]`,
            securityCodeRedirect: `[id^="brq_SERVICE_$giftcardName_"][placeholder*="Security code"]`,
            payNowInline: '#form:visible [type=submit]',

            inlinePinInput: '[name="payment[buckaroo_magento2_giftcards][pin]"]:visible',
            inlineCardNumberInput: '[name="payment[buckaroo_magento2_giftcards][cardnumber]"]:visible',

            submitButton: (card: string) => `#buckaroo_magento2_giftcards_submit_${card}`,

            successHeading: /success/i,
            payRemainingAmountButton: /pay remaining amount/i,

            giftCardsInput: (paymentMethod: string) => `.buckaroo_magento2_${paymentMethod} .buckaroo_magento2_giftcards_input`,
        },
        trustly: {
            oneTimeCodeInputField: '[data-testid="Input-password-challenge_response"]',
            continueBtn: '[data-testid$="continue-button"]',
            continueBtn2: '[data-testid$="account-selector-cta"]',
            oneTimeCode: 'h3',
            checkingAccount: 'text="Checking account"',
            loginId: '[name$="loginid"]',
            snsBank: 'text="SNS Bank"',
            payBtn: '[data-testid$="filled-button-body"]',
        },
        walletMethods: {
            loginButton: "//button[normalize-space()='Login']",
            makePaymentButton: "//button[normalize-space()='Make Payment']",
            backButton: "//button[normalize-space()='Back to where you came from']",
            nextButton: "//button[normalize-space()='Next']",
        },
        payPerEmail: {
            salutationInput: '#buckaroo_magento2_payperemail_genderSelect',
            emailInput: '#buckaroo_magento2_payperemail_Email',
        },
    },

    admin: {
        sidebarMenu: '.admin__menu',
        sidebarStoresMenu: '[data-ui-id="menu-magento-backend-stores"]',
        sidebarConfigurationMenu: '[data-ui-id="menu-magento-config-system-config"]',
        sidebarPaymentMenu: '/admin/system_config/edit/section/payment',
        sidebarBuckarooMenu: '/admin/system_config/edit/section/buckaroo_magento2',

        tabSales: 'role=tab[name=/Sales/i]',
        tabInvoices: '[name="order_invoices"]',

        config: {
            buckarooConfigureButton: '.admin__page-nav-item a[href*="buckaroo_magento2"]',
            paymentMethodsConfigureButton: '[id^="payment_"][id*="_buckaroo_magento2_payment_section-head"]',
            submitButton: '#save',
            messagesSuccess: '.message-success',
            paymentMethodAccordion: (paymentMethod: string) =>
                `#payment_buckaroo_magento2_payment_section >> .entry-edit-head > #payment_buckaroo_magento2_payment_section_buckaroo_magento2_${paymentMethod}-head`,
            paymentMethodAdvancedAccordion: (paymentMethod: string) =>
                paymentMethod.includes('klarna')
                    ? `#payment_buckaroo_magento2_payment_section >> #payment_buckaroo_magento2_payment_section_buckaroo_magento2_klarna_buckaroo_magento2_${paymentMethod}_buckaroo_magento2_advanced-head`
                    : `#payment_buckaroo_magento2_payment_section >> #payment_buckaroo_magento2_payment_section_buckaroo_magento2_${paymentMethod}_buckaroo_magento2_advanced-head`,
            klarnaVariantPaymentMethodAccordion: (klarnaSubMethod: string) =>
                `#payment_buckaroo_magento2_payment_section_buckaroo_magento2_klarna_buckaroo_magento2_${klarnaSubMethod}-head`,
            paymentFee: (paymentMethod: string) =>
                paymentMethod.includes('klarna')
                    ? `#payment_buckaroo_magento2_payment_section_buckaroo_magento2_klarna_buckaroo_magento2_${paymentMethod}_buckaroo_magento2_advanced_payment_fee`
                    : `#payment_buckaroo_magento2_payment_section_buckaroo_magento2_${paymentMethod}_buckaroo_magento2_advanced_payment_fee`,
            giftCardCheckoutOption: '#payment_buckaroo_magento2_payment_section_buckaroo_magento2_giftcards_group_giftcards',
        },

        auth: {
            usernameInput: 'input[name="login[username]"]',
            passwordInput: 'input[name="login[password]"]',
            submitButton: '.action-login.action-primary',
        },

        ordersTable: {
            loader: `[data-bind="scope: 'sales_order_grid.sales_order_grid'"]:visible .admin__data-grid-loading-mask:visible`,
            searchInput: '#fulltext:visible',
            findById: (orderId: string) => `.data-grid-cell-content:has-text("${orderId}")`,
            tableRows: 'tr.data-row',
        },
        orderDetailed: {
            linkCreditMemo: '[data-ui-id="sales-invoice-view-credit-memo-button"]',
            status: '#order_status',
            titleHeading: (orderId: string) => `h1.page-title:has-text("#${orderId}")`,
            orderHistory: '#order_history_block',
            notes: '#order_history_block ul.note-list > li.note-list-item',
            sidebar: '[data-ui-id="sales-order-tabs-tab-sales-order-view-tabs"]',
            shipAction: '#order_ship',
            submitShipmentButton: '.action-default.scalable.save.submit-button.primary',

            invoices: {
                refundOrderItems: '.order-creditmemo-tables > tbody',
                refundUpdateQty: '[data-ui-id="order-items-update-button"]',
                refundButton: "button:has-text('Refund')",
                loadingSpinner: '[data-component="sales_order_view_invoice_grid.sales_order_view_invoice_grid.sales_order_invoice_columns"]',
                table: '#sales_order_view_tabs_order_invoices_content',
                findInvoiceById: (orderId: string) =>
                    `#sales_order_view_tabs_order_invoices_content tr.data-row:has(td .data-grid-cell-content:has-text("${orderId}"))`,
                emptyTable: '#sales_order_view_tabs_order_invoices_content .data-grid-tr-no-data',

                newInvoiceButton: '#order_invoice',
                submitInvoiceButton: '[data-ui-id="order-items-submit-button"][title="Submit Invoice"]',
            },

            orderSummary: {
                subtotal: '.order-subtotal-table tbody tr:has(td:has-text("Subtotal")) span.price',
                shippingFee: '.order-subtotal-table tbody tr:has(td:has-text("Shipping & Handling")) span.price',
                buckarooFee: '.order-subtotal-table tbody tr:has(td:has-text("Payment Fee")) span.price',
                totalTax: '.order-subtotal-table tbody tr:has(td:has-text("Tax")) span.price',
                paidAmount: '.order-subtotal-table tfoot tr:has(td:has-text("Total Paid")) span.price',
                grandTotal: '.order-subtotal-table tfoot tr:has(td:has-text("Grand Total")) span.price',
                totalDue: '.order-subtotal-table tfoot tr:has(td:has-text("Total Due")) span.price',
            },
        },
    },
};
