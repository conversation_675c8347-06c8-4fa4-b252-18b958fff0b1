import wooPlugin from '@woo/WooPlugin';
import { StorefrontBasePage } from '@woo/pages/storefront/index';

export default class OrderSuccessfulPage extends StorefrontBasePage {
    /**
     * Extracts the order number from the current page.
     * @returns The extracted order number.
     */
    public async extractOrderNumber(): Promise<string> {
        return (await wooPlugin.helper.getText(wooPlugin.getSelector('storefront.successfulPage.orderNumber'), {
            strict: true,
            errorMessage: 'Order number element not found.',
        })) as string;
    }

    /**
     * Waits for the success page URL to be loaded.
     */
    public async waitForSuccessPage(): Promise<void> {
        console.log('Waiting for success page...');
        await wooPlugin.page.waitForURL(/.*\/checkout\/order-received\/.*/, { timeout: 30000 });
        const orderNumber = await this.extractOrderNumber();

        console.log(`Order number: ${orderNumber}`);
        wooPlugin.sharedData['orderNumber'] = orderNumber;
    }
}
