import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('Przelewy24 Tests', () => {
    test('Place order with Przelewy24', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
        });
    });

    test('Place order with <PERSON><PERSON>elewy24 (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with <PERSON><PERSON><PERSON>wy24 (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'free',
        });
        await refundService.executeSteps();
    });

    test('Partial refund order with Przelewy24 (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Przelewy24 (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
        });
        await plazaRefundService.executeSteps();
    });

    test('Place order with Przelewy24 (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('p24', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.PRZELEWY24),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Przelewy24 (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('p24', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.PRZELEWY24),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with Przelewy24 (Rejected)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            paymentGatewayOptions: { responseStatus: '690' },
        });
    });

    test('Place order with Przelewy24 (Cancelled by user)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });
});
