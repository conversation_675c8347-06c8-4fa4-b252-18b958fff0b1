import { Page } from '@playwright/test';
import { AdminOrderDetailsPage } from '@woo/pages/admin';
import { ExecuteStepsParams } from 'core/services/workflow';

export enum RefundSteps {
    StorefrontLogin = 'storefrontLogin',
    GoToOrder = 'goToOrder',
    ProcessRefund = 'processRefund',
    VerifyRefund = 'verifyRefund',
}

export type RefundPlacementPages = {
    page: Page;
    adminOrderDetailsPage: AdminOrderDetailsPage;
};

export interface RefundExecuteStepsParams extends ExecuteStepsParams<RefundSteps> {
    partial?: boolean;
}
