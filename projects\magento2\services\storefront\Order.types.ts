import { Page } from '@playwright/test';
import { CheckoutPage, HomePage, LoginPage, OrderSuccessfulPage, ShippingAndBillingPage } from '@magento2/pages/storefront';
import { AdminLoginPage, AdminOrderDetailsPage } from '@magento2/pages/admin';
import { ExecuteStepsParams, PaymentGatewayOptions } from 'core/services/workflow';
import AdminOrderCreateInvoice from '@magento2/pages/admin/AdminOrderCreateInvoice';

export interface Product {
    id: string;
    quantity?: number;
    sizeOptionId?: string;
    colorOptionId?: string;
}

export enum OrderSteps {
    StorefrontLogin = 'storefrontLogin',
    AddItemToCart = 'addItemToCart',
    GoToCart = 'goToCart',
    ProceedToCheckout = 'proceedToCheckout',
    SelectShippingMethod = 'selectShippingMethod',
    ReviewAndPayments = 'reviewAndPayments',
    SelectPaymentMethod = 'selectPaymentMethod',
    PlaceOrder = 'placeOrder',
    SuccessPage = 'successPage',
    AdminLogin = 'adminLogin',
    AdminExtractBckTransaction = 'adminExtractBckTransaction',
    AdminVerifyOrder = 'adminVerifyOrder',
    GoToOrder = 'goToOrder',
    AdminVerifyOrderStatus = 'adminVerifyOrderStatus',
    BuckarooTransactionVerify = 'buckarooTransactionVerify',
    AdminCreateShipmentInvoice = 'adminCreateShipmentInvoice',
}

export type orderPlacementPages = {
    loginPage: typeof LoginPage;
    homePage: typeof HomePage;
    shippingAndBillingPage: typeof ShippingAndBillingPage;
    checkoutPage: typeof CheckoutPage;
    orderSuccessfulPage: typeof OrderSuccessfulPage;
    adminOrderDetailsPage: typeof AdminOrderDetailsPage;
    adminLoginPage: typeof AdminLoginPage;
    adminOrderCreateInvoice: typeof AdminOrderCreateInvoice;
};

export type OrderPlacementPages = { page: Page } & {
    [K in keyof orderPlacementPages]: InstanceType<orderPlacementPages[K]>;
};

export interface OrderExecuteStepsParams<TPaymentGatewayOptions extends PaymentGatewayOptions>
    extends ExecuteStepsParams<OrderSteps, TPaymentGatewayOptions> {
    paymentGatewayOptions?: TPaymentGatewayOptions;
    shippingMethod?: 'free' | 'flat';
    products?: Product[];
}

export interface OrderSummary {
    subtotal: number | null;
    discount?: number | null;
    buckarooPaidAmount?: number | null;
    alreadyPaid?: number | null;
    shippingFee: number | null;
    orderTotal: number | null;
    remainingAmount?: number | null;
    paidAmount?: number | null;
    buckarooFee?: number | null;
    totalTax?: number | null;
    currency: string | null;
}
