import { Locator, <PERSON> } from '@playwright/test';

export type NavigationStep = { title?: string; section?: string; hrefPart?: string };

export type ConfigValue =
    | {
          key: string;
          value: any;
          method: keyof Locator;
      }
    | ((page: Page) => Promise<void>);

export type TransactionStatus = 'success' | 'rejected' | 'canceled' | 'pendingProcessing' | 'failed';
