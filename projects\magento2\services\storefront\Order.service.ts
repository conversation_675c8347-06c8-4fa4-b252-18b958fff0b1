import { CheckoutPage, HomePage, LoginPage, ShippingAndBillingPage } from '@magento2/pages/storefront';
import { AdminLoginPage } from '@magento2/pages/admin';
import { PaymentGatewayOptions, ProcessFlowService, Step } from 'core/services/workflow';
import { OrderExecuteStepsParams, OrderPlacementPages, OrderSteps } from '@magento2/services/storefront/Order.types';
import magentoPlugin from '@magento2/MagentoPlugin';
import AdminOrderCreateInvoice from '@magento2/pages/admin/AdminOrderCreateInvoice';
import OrderSuccessfulPage from '../../pages/storefront/OrderSuccessfulPage';
import AdminOrderDetailsPage from '../../pages/admin/AdminOrderDetailsPage';

export default class OrderService<TPaymentGatewayOptions extends PaymentGatewayOptions> extends ProcessFlowService<
    OrderSteps,
    OrderPlacementPages,
    OrderExecuteStepsParams<TPaymentGatewayOptions>
> {
    constructor() {
        super();

        this.pages.loginPage = new LoginPage();
        this.pages.homePage = new HomePage();
        this.pages.shippingAndBillingPage = new ShippingAndBillingPage();
        this.pages.checkoutPage = new CheckoutPage();
        this.pages.orderSuccessfulPage = new OrderSuccessfulPage();
        this.pages.adminLoginPage = new AdminLoginPage();
        this.pages.adminOrderDetailsPage = new AdminOrderDetailsPage();
        this.pages.adminOrderCreateInvoice = new AdminOrderCreateInvoice();
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<OrderSteps>[] {
        return [
            {
                name: OrderSteps.StorefrontLogin,
                action: async () => {
                    await this.pages.loginPage.login();
                },
            },
            {
                name: OrderSteps.AddItemToCart,
                action: async () => {
                    await this.pages.homePage.goToHomePage();
                    await this.pages.homePage.addItemsToCart(this.options.products);
                },
            },
            {
                name: OrderSteps.GoToCart,
                action: async () => {
                    await this.pages.homePage.goToCart();
                    console.log(`Selecting shipping address for payment method: ${this.options.paymentMethod}`);
                },
            },
            {
                name: OrderSteps.ProceedToCheckout,
                action: async () => {
                    await this.pages.shippingAndBillingPage.proceedToCheckout();
                },
            },
            {
                name: OrderSteps.SelectShippingMethod,
                action: async () => {
                    await this.pages.shippingAndBillingPage.fillFormForGuest();
                    await this.pages.shippingAndBillingPage.selectShippingMethod(this.options.shippingMethod);
                },
            },
            {
                name: OrderSteps.ReviewAndPayments,
                action: async () => {
                    await this.pages.checkoutPage.reviewAndPayments();
                },
            },
            {
                name: OrderSteps.SelectPaymentMethod,
                action: async () => {
                    if (!this.options.paymentMethod) throw new Error('Payment method is required');

                    await this.pages.checkoutPage.selectPaymentMethod(this.options.paymentMethod);

                    if (this.options.paymentGatewayOptions?.paymentFee) {
                        await this.pages.checkoutPage.checkPaymentFee(this.options.paymentGatewayOptions.paymentFee);
                    }
                },
            },
            {
                name: OrderSteps.PlaceOrder,
                action: async () => {
                    await this.pages.checkoutPage.placeOrder();
                },
            },
            {
                name: OrderSteps.SuccessPage,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.orderSuccessfulPage.waitForSuccessPage();

                    const orderNumber = magentoPlugin.sharedData['orderNumber'];
                    this.pages.adminOrderDetailsPage.setOrderNumber(orderNumber);
                },
            },
            {
                name: OrderSteps.AdminLogin,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.adminLoginPage.login();
                },
            },
            {
                name: OrderSteps.GoToOrder,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.findOrderByID();
                },
            },
            {
                name: OrderSteps.AdminExtractBckTransaction,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.extractBuckarooTransaction();
                },
            },
            {
                name: OrderSteps.AdminVerifyOrderStatus,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.verifyOrderStatus('Processing');
                    await this.pages.adminOrderDetailsPage.verifyOrderTotalPaid();
                },
            },
            {
                name: OrderSteps.BuckarooTransactionVerify,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.verifyBuckarooTransaction();
                },
            },
        ];
    }
}
