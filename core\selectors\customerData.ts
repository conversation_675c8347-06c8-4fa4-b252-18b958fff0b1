export type Gender = 'male' | 'female';

export interface CustomerData {
    email: string;
    firstName: string;
    lastName: string;
    company: string;
    street1: string;
    street2: string;
    country: string;
    state: string;
    city: string;
    zip: string;
    phone: string;
    genre: string;
    dob: string;
}

export const customerData: CustomerData = {
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'Acceptatie',
    company: 'Buckaroo',
    street1: 'Afleverstraat',
    street2: '1',
    country: 'NL',
    state: 'Noord-Holland',
    city: 'Heerenveen',
    zip: '8441ER',
    phone: '0201234567',
    dob: '01/01/1990',
    genre: 'female',
};
