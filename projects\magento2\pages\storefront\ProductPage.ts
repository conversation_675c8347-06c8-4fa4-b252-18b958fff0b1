import { expect, Locator } from '@playwright/test';
import { env } from '@/utils/env';
import magentoPlugin from '@magento2/MagentoPlugin';
import { StorefrontBasePage } from '@magento2/pages/storefront/index';
import { Product } from '@magento2/services/storefront';

export default class ProductPage extends StorefrontBasePage {
    /**
     * Navigates to a specific URL.
     * @param productId
     */
    async navigateToProduct(productId: string): Promise<void> {
        await magentoPlugin.page.goto(env('BASE_URL') + '/catalog/product/view/id/' + productId);
    }

    /**
     * Adds a single item to the cart by selecting size, color, and quantity automatically.
     * If productId, sizeOptionId, or colorOptionId are not provided, selects the first available option.
     * @param product - The product details.
     */
    async addSingleItemToCart(product: Product): Promise<void> {
        let { id: productId, quantity = 1, sizeOptionId, colorOptionId } = product;

        await this.navigateToProduct(productId);

        const productName = await magentoPlugin.helper.getText(magentoPlugin.getSelector('storefront.product.itemTitle'));

        // Select size if applicable
        if (sizeOptionId) {
            await magentoPlugin.helper.clickAndExpectVisible(
                magentoPlugin.page.locator(
                    (magentoPlugin.getSelector('storefront.product.selectSizeOption') as (optionId: string) => string)(sizeOptionId)
                ),
                `Size option with ID '${sizeOptionId}' is not visible.`
            );
        } else {
            await magentoPlugin.helper.clickAndExpectVisible(
                magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.product.selectSizeOptions')).first(),
                'No size options available.'
            );
        }

        // Select color if applicable
        if (colorOptionId) {
            await magentoPlugin.helper.clickAndExpectVisible(
                magentoPlugin.page.locator(
                    (magentoPlugin.getSelector('storefront.product.selectColorOption') as (optionId: string) => string)(colorOptionId)
                ),
                `Color option with ID '${colorOptionId}' is not visible.`
            );
        } else {
            await magentoPlugin.helper.clickAndExpectVisible(
                magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.product.selectColorOptions')).first(),
                'No color options available.'
            );
        }

        // Set quantity
        const quantityLocator = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.product.itemQuantity'));

        await magentoPlugin.helper.expectToBeVisible(quantityLocator, {
            errorMessage: 'Quantity input is not visible.',
        });
        await quantityLocator.fill(quantity.toString());

        // Add to cart
        await magentoPlugin.helper.clickAndExpectVisible('storefront.product.addToCart', 'Add to Cart button is not visible.');

        // Verify success message
        const successMessageLocator = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.checkout.itemAddedSuccessMessage'));
        await expect(successMessageLocator, `Success message not found after adding '${productName}' to the cart.`).toContainText(
            `You added ${productName} to your shopping cart.`,
            { ignoreCase: true, timeout: 30000 }
        );

        console.log(`Added ${quantity} item(s) '${productName}' to your shopping cart.`);
    }

    /**
     * Retrieves the ID of a random product from the homepage.
     * @returns {Promise<string>} The ID of a random product.
     */
    public async getRandomProductId(): Promise<string> {
        const firstProductLocator: Locator = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.product.firstProductItem'));

        await magentoPlugin.helper.expectToBeVisible(firstProductLocator, {
            errorMessage: 'No products found on the homepage.',
        });

        const priceBoxLocator = firstProductLocator.locator('div.price-box[data-product-id]');
        const dataProductId = await priceBoxLocator.getAttribute('data-product-id');

        if (!dataProductId) {
            throw new Error('First product does not have a data-product-id attribute.');
        }

        return dataProductId;
    }
}
