import { defineConfig } from '@playwright/test';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';

dotenvExpand.expand(dotenv.config());

dotenvExpand.expand(dotenv.config({ path: `.env.woo`, override: true }));

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    testDir: './tests',
    workers: 1,
    timeout: 6 * 60 * 1000, // 6 minutes
    use: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36',
        headless: false,
        viewport: null, // Set to null to maximize the window
        ignoreHTTPSErrors: true,
        launchOptions: {
            args: ['--start-maximized'], // Add this line to maximize the window
        },
    },
    projects: [
        {
            name: 'chromium',
            use: { browserName: 'chromium' },
        },
    ],
});
