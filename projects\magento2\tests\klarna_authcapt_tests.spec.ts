import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage, AdminRefundPage } from '@magento2/pages/admin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { env } from '@/utils/env';
import { AdminPlazaRefundSteps } from '@magento2/services/admin/AdminPlazaRefund.types';

test.describe('Klarna Pay later (authorize/capture) Tests', () => {
    test('Place order with Klarna Pay later (authorize/capture)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
        });
    });

    test('Place order with Klarna Pay later (authorize/capture) (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
            disabledSteps: [OrderSteps.StorefrontLogin],
        });
    });

    test('Refund order with Klarna Pay later (authorize/capture) - (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
        });
        await refundService.executeSteps();
    });

    test('Partial refund order with Klarna Pay later (authorize/capture) - (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
            partial: true,
        });
    });

    test('Refund order with Klarna Pay later (authorize/capture) (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
        });

        await plazaRefundService.executeSteps({
            disabledSteps: [AdminPlazaRefundSteps.VerifyRefundOnAdmin],
            hooks: {
                [AdminPlazaRefundSteps.GoToOrder]: {
                    after: async () => {
                        await new AdminRefundPage().verifyPlazaRefundSuccess('Complete');
                    },
                },
            },
        });
    });

    test('Place order with Klarna Pay later (authorize/capture) (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('klarnakp', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KLARNA_AUTHCAPT),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Klarna Pay later (authorize/capture) (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('klarnakp', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KLARNA_AUTHCAPT),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});

test('Place order with Klarna Authorize Capture - with content type "soap"', async ({ orderService }) => {
    test.skip(env('STAGE') !== 'refactor', 'Skipping test because STAGE is not "refactor".');

    await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.soap);

    await orderService.executeSteps({
        paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
    });
});

test('Place order with Klarna Authorize Capture - with content type "json"', async ({ orderService }) => {
    await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

    await orderService.executeSteps({
        paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
    });
});

test('Place order with Klarna Authorize Capture - with content type "httppost"', async ({ orderService }) => {
    await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

    await orderService.executeSteps({
        paymentMethod: PaymentMethods.KLARNA_AUTHCAPT,
    });
});
