import { In3PaymentMethod as BaseIn3PaymentMethod } from '@core/pages/payments/methods';
import { expect } from '@woo/fixtures/BaseTest';
import { OrderSteps } from '@woo/services/storefront';
import wooPlugin from '@woo/WooPlugin';

export default class In3PaymentMethod extends BaseIn3PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        await this.checkoutPage.fillDateOfBirth('01/01/1990');
    }

    /**
     * Handles actions before placing the order with a negative scenario.
     */
    async beforeNegativePlaceOrder(): Promise<void> {
        // await (this.checkoutPage as any).fillDateOfBirth('01/01/1990');
        await this.checkoutPage.fillDateOfBirth('01/01/1990');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.acceptTermsAndConditions();
        await this.orderNow();
        await this.submitStatus();
        await this.finalizeOrder();
    }

    /**
     * Handles actions after placing the order with a negative scenario.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);

        const responseStatus = this.options.responseStatus ?? '791';

        await this.acceptTermsAndConditions();
        await this.orderNow();
        await this.submitStatus(responseStatus);

        switch (responseStatus) {
            case '791':
                await this.verifyIn3PaymentError('payment pending');
                break;
            case '490':
            case '690':
            case '890':
                await this.verifyIn3PaymentError('unfortunately your payment was not successful');
                break;
        }

        console.log('Order failed as expected');
    }

    /**
     * Verifies the In3 payment error message for negative test cases.
     * @param expectedMessage - The expected error message.
     */
    protected async verifyIn3PaymentError(expectedMessage: string): Promise<void> {
        console.log('Verifying In3 payment error message…');
        const errorSelector = wooPlugin.getSelector('storefront.checkout.in3PaymentErrorMessage');

        await wooPlugin.helper.expectToBeVisible(errorSelector, { timeout: 10000 });

        expect((await wooPlugin.page.locator(errorSelector).textContent())?.toLowerCase()).toContain(expectedMessage);
        console.log('Order failed as expected');
    }
}
