import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { PaymentGatewayOptions } from 'core/services/workflow';

export default class PaypalPaymentMethod<TOptions = PaymentGatewayOptions> extends PaymentMethod<TOptions> {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.processPayPalPayment('<EMAIL>', 'Cyb3rt3ch');
        await this.checkoutPage.continueToReviewOrder();
    }

    /**
     * Processes PayPal payment by filling credentials and logging in.
     * @param email - The email address to use for login.
     * @param password - The password to use for login.
     */
    protected async processPayPalPayment(email: string, password: string): Promise<void> {
        await this.fillField(magentoPlugin.getSelector('paymentMethods.payPal.email'), email, 'Email');
        await this.clickButton(magentoPlugin.getSelector('paymentMethods.payPal.clickNext'), 'Next');
        await this.fillField(magentoPlugin.getSelector('paymentMethods.payPal.password'), password, 'Password');
        await this.clickButton(magentoPlugin.getSelector('paymentMethods.payPal.loginBtn'), 'Login');
    }

    /**
     * Fills a field with the specified value.
     * @param selector - The selector for the field.
     * @param value - The value to fill.
     * @param fieldName - The name of the field for logging purposes.
     */
    protected async fillField(selector: string, value: string, fieldName: string): Promise<void> {
        await magentoPlugin.page.fill(selector, value);
        console.log(`Filled ${fieldName}`);
    }

    /**
     * Clicks a button with the specified selector.
     * @param selector - The selector for the button.
     * @param actionName - The name of the action for logging purposes.
     */
    protected async clickButton(selector: string, actionName: string): Promise<void> {
        await magentoPlugin.page.click(selector);
        console.log(`${actionName} button clicked`);
    }
}
