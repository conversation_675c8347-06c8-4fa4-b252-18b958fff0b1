import { OrderService } from '@magento2/services/storefront';
import { AdminPlazaRefundService, AdminRefundService } from '@magento2/services/admin';
import { PlazaTestScenarioService } from 'core/services/buckaroo';

export type CustomFixtures = {
    orderService: InstanceType<typeof OrderService>;
    refundService: InstanceType<typeof AdminRefundService>;
    plazaRefundService: InstanceType<typeof AdminPlazaRefundService>;
    plazaTestScenarioService: InstanceType<typeof PlazaTestScenarioService>;
};
