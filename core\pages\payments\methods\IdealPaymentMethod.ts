import { PaymentMethod } from '@core/services/payments';
import { IdealOptions } from '@core/pages/payments/methods/types';
import { globalContext } from '@core/context/TestContext';

export default class IdealPaymentMethod extends PaymentMethod<IdealOptions> {
    /**
     * Clicks the "Submit Status" button.
     * @param status - The status code to submit. Defaults to '190'.
     */
    async submitStatus(status: any = '190'): Promise<void> {
        await globalContext.page.selectOption(globalContext.getSelector('payments.ideal.selectStatus'), status);
        await globalContext.helper.waitForVisibleAndClick('payments.ideal.submitStatus', 'Submitting status.');
    }
}
