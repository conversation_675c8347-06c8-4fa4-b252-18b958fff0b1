import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class TrustlyPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.completePaymentSteps();
    }

    protected async completePaymentSteps(): Promise<void> {
        const trustlySelectors = magentoPlugin.getSelector('paymentMethods.trustly') as Record<string, string>;

        await magentoPlugin.page.click(trustlySelectors.payBtn);
        await magentoPlugin.page.click(trustlySelectors.snsBank);
        await magentoPlugin.page.click(trustlySelectors.continueBtn);
        await magentoPlugin.page.fill(trustlySelectors.loginId, '1234');
        await magentoPlugin.page.click(trustlySelectors.continueBtn);

        const oneTimeCode = await this.getOneTimeCode(trustlySelectors.oneTimeCode);
        await this.fillOneTimeCode(oneTimeCode);

        await magentoPlugin.page.click(trustlySelectors.checkingAccount);
        await magentoPlugin.page.click(trustlySelectors.continueBtn2);
        await magentoPlugin.page.click(trustlySelectors.continueBtn);

        const oneTimeCode2 = await this.getOneTimeCode(trustlySelectors.oneTimeCode);
        await this.fillOneTimeCode(oneTimeCode2);
    }

    protected async getOneTimeCode(codeLocator: string): Promise<string | null> {
        // Wait until the text content of the element matches the 6-digit format
        await magentoPlugin.page.waitForFunction(locator => {
            const element = document.querySelector(locator);
            return element && element.textContent && /^\d{6}$/.test(element.textContent.trim());
        }, codeLocator);

        const code = await magentoPlugin.page.evaluate(locator => {
            const element = document.querySelector(locator);
            return element ? element.textContent?.trim() : null;
        }, codeLocator);

        console.log(`One Time Code is: ${code}`);
        return code && /^\d{6}$/.test(code) ? code : null;
    }

    protected async fillOneTimeCode(oneTimeCode: string | null): Promise<void> {
        const trustlySelectors = magentoPlugin.getSelector('paymentMethods.trustly') as Record<string, string>;
        if (oneTimeCode) {
            await magentoPlugin.page.fill(trustlySelectors.oneTimeCodeInputField, oneTimeCode);
            await magentoPlugin.page.click(trustlySelectors.continueBtn);
            console.log(`Filled the one-time code: ${oneTimeCode}`);
        } else {
            console.error('Failed to retrieve one-time code.');
            throw new Error('One-time code is null or undefined.');
        }
    }
}
