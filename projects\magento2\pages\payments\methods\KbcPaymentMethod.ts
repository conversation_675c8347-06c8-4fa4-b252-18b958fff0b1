import { PaymentMethod } from '@magento2/services/payments';
import { KbcOptions } from '@magento2/pages/payments/methods/types';
import { OrderSteps } from '@magento2/services/storefront/Order.types';
import { AdminOrderDetailsPage } from '@magento2/pages/admin';

export default class KbcPaymentMethod extends PaymentMethod<KbcOptions> {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.checkoutPage.submitStatus();
    }

    /**
     * Executes the steps to handle unsuccessful order placements.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);

        const responseStatus = this.options.responseStatus ?? '690';

        await this.checkoutPage.submitStatus(responseStatus);

        switch (responseStatus) {
            case '490':
            case '690':
                await this.checkoutPage.verifyPaymentError('unfortunately an error occurred while processing your payment');
                break;
            case '890':
                await this.checkoutPage.verifyPaymentError('you have canceled the payment');
                break;
        }
    }

    /*
     * Executes the steps to handle unsuccessful order verification after logging in to Admin.
     */
    async afterNegativeAdminLogin(): Promise<void> {
        const responseStatus = this.options.responseStatus ?? '690';

        await new AdminOrderDetailsPage().findLatestOrder();
        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderStatus('Canceled');
        await this.processFlowService.pages.adminOrderDetailsPage.extractBuckarooTransaction();

        switch (responseStatus) {
            case '490':
            case '690':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('rejected');
                break;
            case '890':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('canceled');
                break;
        }
    }
}
