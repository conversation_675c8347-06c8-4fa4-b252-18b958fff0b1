import CardPaymentMethod from '@magento2/pages/payments/methods/CardPaymentMethod';
import { CardInfo, CreditCardOptions } from '@magento2/pages/payments/methods/types';
import magentoPlugin from '@magento2/MagentoPlugin';
import { OrderSteps } from '@magento2/services/storefront/Order.types';
import { AdminOrderDetailsPage } from '@magento2/pages/admin';

const cards: Record<string, { name: string; cardInfo: CardInfo }> = {
    amex: {
        name: 'American Express',
        cardInfo: {
            name: 'Automated Test',
            number: '***************',
            expMonth: '01',
            expYear: '29',
            cvv: '1234',
        },
    },
    visa: {
        name: 'VISA',
        cardInfo: {
            name: 'Automated Test',
            number: '****************',
            expMonth: '01',
            expYear: '29',
            cvv: '123',
        },
    },
    mastercard: {
        name: 'MasterCard',
        cardInfo: {
            name: 'Automated Test',
            number: '****************',
            expMonth: '01',
            expYear: '29',
            cvv: '123',
        },
    },
    bancontact: {
        name: 'Bancontact',
        cardInfo: {
            name: 'Automated Test',
            number: '67034200554565015',
            expMonth: '01',
            expYear: '29',
        },
    },
};

export default class CreditCardPaymentMethod extends CardPaymentMethod<CreditCardOptions> {
    /**
     * Handles actions after selecting the payment method.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        await this.selectCard();
    }

    /**
     * Handles actions after selecting the payment method with a negative scenario.
     */
    async afterNegativeSelectPaymentMethod(): Promise<void> {
        await this.selectCard();
    }

    /**
     * Performs actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.enterCardInfo();
        await this.payNow();
        await this.submitPaymentStatus('Y');
    }

    /**
     *  Performs actions after placing the order with a negative scenario.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);

        await this.enterCardInfo();
        await this.payNow();

        const responseStatus = this.options.responseStatus ?? 'U';

        await this.checkoutPage.submitStatus(responseStatus);
        console.log(`Response status: ${responseStatus}`);

        switch (responseStatus) {
            case 'U':
            case 'N':
                await this.checkoutPage.verifyPaymentError('unfortunately an error occurred while processing your payment');
                break;
        }
    }

    /**
     * Executes the steps to handle unsuccessful order verification after logging in to Admin.
     */
    async afterNegativeAdminLogin(): Promise<void> {
        const responseStatus = this.options.responseStatus ?? 'U';

        await new AdminOrderDetailsPage().findLatestOrder();
        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderStatus('Canceled');
        await this.processFlowService.pages.adminOrderDetailsPage.extractBuckarooTransaction();

        switch (responseStatus) {
            case 'U':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('rejected');
                break;
            case 'N':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('rejected');
                break;
        }
    }

    /**
     * Clicks the "Pay Now" button to proceed with the payment.
     */
    async payNow(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.creditCards.payNowBtn'));
    }

    /**
     * Retrieves card details for the selected card option.
     * @returns The card details object.
     */
    protected getCardDetails(): CardInfo {
        return cards[this.options.card].cardInfo;
    }

    /**
     * Submits the payment status with the specified value.
     * @param status - The payment status to select (e.g., 'Y' for Yes).
     */
    protected async submitPaymentStatus(status: string): Promise<void> {
        await magentoPlugin.page.selectOption(magentoPlugin.getSelector('paymentMethods.creditCards.paymentStatusDropdown'), status);
        await this.checkoutPage.submitStatus('Y');
    }

    /**
     * Selects the card based on the provided card option.
     */
    protected async selectCard(): Promise<void> {
        const cardLocator = magentoPlugin.page.locator(`text=${cards[this.options.card].name}`);
        if (cards[this.options.card].name !== cards.bancontact.name) {
            await magentoPlugin.helper.clickAndExpectVisible(cardLocator, `'${cards[this.options.card].name}' is not found on buckaroo checkout`);
        }
        console.log(`Card "${this.options.card}" is selected!`);
    }
}
