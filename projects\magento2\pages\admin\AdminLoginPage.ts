import BasePage from '@core/pages/BasePage';
import { env } from '@/utils/env';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class AdminLoginPage extends BasePage {
    /**
     * Performs login to the admin panel.
     */
    async login(): Promise<void> {
        const { baseUrl, username, password } = this.getAdminCredentials();

        console.log('Navigating to admin login page...');
        await this.navigateToAdminLogin(baseUrl);

        const alreadyLoggedIn = await this.isAlreadyLoggedIn();
        console.log(`Already logged in: ${alreadyLoggedIn ? 'Yes' : 'No'}`);
        if (alreadyLoggedIn) return;

        await this.performLogin(username, password);
        await magentoPlugin.page.waitForURL(/.*\/backend\/admin\/.*/, { timeout: 10000 });
    }

    /**
     * Retrieves required admin credentials from environment variables.
     */
    protected getAdminCredentials(): { baseUrl: string; username: string; password: string } {
        const adminBaseUrl = env('ADMIN_BASE_URL');
        const adminUsername = env('ADMIN_USER');
        const adminPassword = env('ADMIN_PASSWORD');

        if (!adminBaseUrl || !adminUsername || !adminPassword) {
            throw new Error('ADMIN_BASE_URL, ADMIN_USER, and ADMIN_PASSWORD must be set in environment variables.');
        }

        return { baseUrl: adminBaseUrl, username: adminUsername, password: adminPassword };
    }

    /**
     * Navigates to the admin login page.
     * @param baseUrl - Admin base URL.
     */
    protected async navigateToAdminLogin(baseUrl: string): Promise<void> {
        await magentoPlugin.page.goto(`${baseUrl}/admin`);
    }

    /**
     * Fills the login form and submits it.
     * @param username - Admin username.
     * @param password - Admin password.
     */
    protected async performLogin(username: string, password: string): Promise<void> {
        console.log('Filling login form');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('admin.auth.usernameInput'), username);
        await magentoPlugin.page.fill(magentoPlugin.getSelector('admin.auth.passwordInput'), password);
        await magentoPlugin.page.click(magentoPlugin.getSelector('admin.auth.submitButton'));
        console.log('Admin Login form submitted.');
    }

    /**
     * Verifies if already logged in.
     */
    public async isAlreadyLoggedIn(): Promise<boolean> {
        try {
            return await magentoPlugin.page.locator(magentoPlugin.getSelector('admin.sidebarMenu')).isVisible({ timeout: 5000 });
        } catch {
            return false;
        }
    }
}
