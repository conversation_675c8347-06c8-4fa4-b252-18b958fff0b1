import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { OrderSteps } from '@magento2/services/storefront/Order.types';
import { AdminOrderDetailsPage } from '@magento2/pages/admin';
import { In3Options } from './types';

export default class In3PaymentMethod extends PaymentMethod<In3Options> {
    /**
     * Handles actions after placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        await this.checkoutPage.fillDateOfBirth('01/01/1990');
    }

    /**
     * Handles actions before placing the order with a negative scenario.
     */
    async beforeNegativePlaceOrder(): Promise<void> {
        await this.checkoutPage.fillDateOfBirth('01/01/1990');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.acceptTermsAndConditions();
        await this.orderNow();
        await this.checkoutPage.submitStatus();
        await this.finalizeOrder();
    }

    /**
     * Handles actions after placing the order with a negative scenario.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);
        const responseStatus = this.options.responseStatus ?? '791';

        await this.acceptTermsAndConditions();
        await this.orderNow();
        await this.checkoutPage.submitStatus(responseStatus);

        switch (responseStatus) {
            case '791':
                await this.checkoutPage.verifyPaymentError('unfortunately an error occurred while processing your payment');
                break;
            case '490':
            case '690':
            case '890':
                await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.in3.returnToWebshop'));
                await this.checkoutPage.verifyPaymentError('you have canceled the payment');
                break;
        }
    }

    /**
     * Executes the steps to handle unsuccessful order verification after logging in to Admin.
     */
    async afterNegativeAdminLogin(): Promise<void> {
        const responseStatus = this.options.responseStatus ?? '490';

        await new AdminOrderDetailsPage().findLatestOrder();
        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderStatus('Canceled');
        await this.processFlowService.pages.adminOrderDetailsPage.extractBuckarooTransaction();

        switch (responseStatus) {
            case '490':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('failed');
                break;
            case '690':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('rejected');
                break;
            case '890':
                await this.processFlowService.pages.adminOrderDetailsPage.verifyBuckarooTransaction('canceled');
                break;
        }
    }

    protected async acceptTermsAndConditions(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.in3.checkConditions'));
    }

    protected async orderNow(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.in3.orderNow'));
    }

    protected async finalizeOrder() {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.in3.finalizeOrder'));
    }
}
