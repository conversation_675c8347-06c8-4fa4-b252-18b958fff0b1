import { PaymentMethod } from '@core/services/payments';
import { globalContext } from '@core/context/TestContext';
import { In3Options } from '@core/pages/payments/methods/types';
import CheckoutPage from '@woo/pages/storefront/CheckoutPage';
import wooPlugin from '@woo/WooPlugin';

export default class In3PaymentMethod extends PaymentMethod<In3Options, CheckoutPage> {
    /**
     * Clicks the "Submit Status" button.
     * @param status - The status code to submit. Defaults to '190'.
     */
    async submitStatus(status: any = '190'): Promise<void> {
        await globalContext.page.selectOption(globalContext.getSelector('payments.ideal.selectStatus'), status);
        await globalContext.helper.waitForVisibleAndClick('payments.ideal.submitStatus', 'Submitting status.');
    }

    /*
     * This method clicks the checkbox to accept the terms and conditions.
     */
    async acceptTermsAndConditions(): Promise<void> {
        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.in3.checkConditions'));
    }

    async orderNow(): Promise<void> {
        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.in3.orderNow'));
    }

    async finalizeOrder() {
        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.in3.finalizeOrder'));
    }
}
