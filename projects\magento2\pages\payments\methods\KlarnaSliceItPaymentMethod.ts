import { FrameLocator } from 'playwright-core';
import { Locator } from '@playwright/test';
import { KlarnaPaymentMethod } from '@magento2/pages/payments';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class KlarnaSliceItPaymentMethod extends KlarnaPaymentMethod {
    /**
     * Handles actions before proceeding to checkout.
     */
    async afterProceedToCheckout(): Promise<void> {
        await this.addGermanyShippingAddress();
    }

    /**
     * Handles actions before placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        await super.selectCustomerGender('male');
        await super.toggleBillingSameAsShipping();
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.processKlarnaPayment('+49017614284340', '123456');
        await this.handleKlarnaSliceItPaymentFlow();
    }

    /**
     * Handles the payment flow for Klarna Slice It.
     */
    protected async handleKlarnaSliceItPaymentFlow(): Promise<void> {
        const iframeLocator: FrameLocator = magentoPlugin.page.frameLocator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.iFrame'));
        const chooseHowToPayPage: Locator = magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.chooseHowToPayView'));

        await magentoPlugin.page.waitForTimeout(4000);

        if (await chooseHowToPayPage.isVisible()) {
            await this.handleChooseHowToPayPage();
        } else {
            await this.clickBuyButton();
        }

        if (await iframeLocator.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.usernameIframe')).isVisible()) {
            await this.handleIframePayment(iframeLocator);
        }
    }

    /**
     * Handles the "Choose how to pay" page in the Klarna Slice It payment flow.
     */
    protected async handleChooseHowToPayPage(): Promise<void> {
        console.log("'Choose how to pay ..' page is opened.");
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.continueBtn'));
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.continueBtn'));
        await this.clickBuyButton();
    }

    /**
     * Handles payment within the Klarna Slice It iframe.
     * @param iframeLocator - The locator for the Klarna Slice It iframe.
     */
    protected async handleIframePayment(iframeLocator: FrameLocator): Promise<void> {
        console.log('iFrame is opened.');
        const usernameField = iframeLocator.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.usernameIframe'));
        const passwordField = iframeLocator.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.passwordIframe'));
        const otpField = iframeLocator.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.otpIframe'));
        const continueButton = iframeLocator.locator(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.continueBtnIframe'));

        await usernameField.fill('Test');
        await passwordField.fill('123456');
        await continueButton.click();
        await otpField.fill('1234');
        await continueButton.click();
    }

    /**
     * Adds a new shipping address specific to Klarna Slice It payments for Germany, ensuring it is not saved in the address book
     */
    protected async addGermanyShippingAddress(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.newAddressBtn'));
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.firstName'), 'Mock');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.lastName'), 'Mock');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.address'), 'Neue Schönhauser Str. 2');
        await magentoPlugin.page.selectOption(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.country'), { label: 'Germany' });
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.city'), 'Berlin');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.postcode'), '10178');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.phoneNr'), '+49017614284340');
        await this.disableAddressSave();
        await this.clickOnShipHereButton();
    }

    /**
     * Ensures the address is not saved to avoid accumulation of addresses in the Shipping and Billing Page.
     */
    protected async disableAddressSave(): Promise<void> {
        await magentoPlugin.page.uncheck(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.saveAddressCheckbox'));
    }

    protected async clickOnShipHereButton(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.shipHereBtn'));
    }
}
