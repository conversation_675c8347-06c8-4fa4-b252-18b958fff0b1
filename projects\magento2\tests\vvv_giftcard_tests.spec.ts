import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';

test.describe('VVV Giftcard Tests', () => {
    test('Place order with VVV (redirect)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'vvvgiftcard', amount: 10 },
        });
    });

    test('Place order with VVV (redirect) - (with multiple products)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'vvvgiftcard', amount: 10 },
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });
    });

    test('Refund order with VVV (redirect) - (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'vvvgiftcard', amount: 10 },
        });

        await plazaRefundService.executeSteps({});
    });

    test('Place order with VVV Giftcard (inline)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.VVVGIFTCARD,
            paymentGatewayOptions: { amount: 10 },
        });
    });

    test('Place order with VVV (inline) - (with multiple products)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.VVVGIFTCARD,
            paymentGatewayOptions: { amount: 10 },
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });
    });

    test('Refund order with VVV (inline) - (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.VVVGIFTCARD,
            paymentGatewayOptions: { amount: 10 },
        });

        await plazaRefundService.executeSteps({});
    });
});
