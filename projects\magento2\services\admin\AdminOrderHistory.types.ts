import { ParseResult } from '@core/services/moneyParser/types';
import moment from 'moment';
import { Moment } from 'moment/moment';

export interface TransactionInfo {
    date: string;
    time: string;
    status: string;
    comment: string;
    bckTransactionKey?: string;
    amount?: ParseResult | null;
    isRefund?: boolean;
    isPayment?: boolean;
    isCapture?: boolean;
    datetime?: moment.Moment;
}

export interface TransactionFilter {
    fromDate?: Moment;
    toDate?: Moment;
    minAmount?: number;
    maxAmount?: number;
    isRefund?: boolean;
    isPayment?: boolean;
    isCapture?: boolean;
    bckTransactionKey?: string;
    commentContains?: string;
    hasBckTransactionKey?: boolean;
}
