import BasePage from '@core/pages/BasePage';
import { AdminOrderHistoryService, AdminOrderService } from '@magento2/services/admin';
import magentoPlugin from '@magento2/MagentoPlugin';
import { expect } from '@magento2/fixtures/BaseTest';
import { get, isNil } from 'lodash-es';
import { TransactionInfo } from '@magento2/services/admin/AdminOrderHistory.types';
import { TransactionStatusService } from 'core/services/buckaroo';
import OrderSummaryService from '@core/services/workflow/OrderSummary.service';
import { TransactionStatus } from './types';
import { TransactionResponse } from '@buckaroo/buckaroo_sdk';

export default class AdminOrderDetailsPage extends BasePage {
    private readonly adminOrderService: AdminOrderService;
    public adminOrderHistoryService!: AdminOrderHistoryService;
    public orderNumber!: string;

    protected paidTransactions!: TransactionInfo;

    constructor() {
        super();
        this.adminOrderService = new AdminOrderService();
        this.adminOrderHistoryService = new AdminOrderHistoryService();
    }

    public setOrderNumber(orderNumber: string) {
        this.orderNumber = orderNumber;
        return this;
    }

    /**
     * Retrieves the status of an order.
     * @returns The trimmed text content of the order status.
     */
    public async getOrderStatus(): Promise<string | null> {
        return await magentoPlugin.helper.getText(magentoPlugin.getSelector('admin.orderDetailed.status'), {
            errorMessage: 'Order status element not found.',
            strict: true,
        });
    }

    /**
     * Verifies that the order status matches the expected status.
     * @param expectedStatus - The expected order status (e.g., 'Closed').
     */
    public async verifyOrderStatus(expectedStatus: string = 'Processing'): Promise<void> {
        console.log('Verifying order details.');
        await magentoPlugin.helper.executeWithRetry(
            async () => {
                const orderStatus = await this.getOrderStatus();
                await expect(orderStatus, `The Order status is expected to be '${expectedStatus}', but it is '${orderStatus}'`).toBe(expectedStatus);
            },
            {
                retries: 5,
                delay: 2000,
                errorMessage: 'Failed to click the button after 5 attempts.',
                logMessage: 'Clicking the button',
            }
        );
    }

    /**
     * Finds an order by its ID in the admin interface.
     */
    public async findOrderByID(): Promise<void> {
        const adminOrderUrl = get(magentoPlugin.sharedData['adminOrderUrls'], this.orderNumber);

        if (adminOrderUrl) {
            await magentoPlugin.page.goto(adminOrderUrl);
            console.log(`Navigated to the order detailed page using the stored URL: ${adminOrderUrl}`);
            return;
        }

        console.log(`Searching for order with ID ${this.orderNumber} in Admin.`);
        await this.adminOrderService.navigateToAdminPage('/sales/order');
        await magentoPlugin.helper.waitToBeHidden('admin.ordersTable.loader');

        const orderRow = magentoPlugin.page.locator(magentoPlugin.getSelector('admin.ordersTable.findById')(this.orderNumber));
        console.log(`Checking if order with ID ${this.orderNumber} already exists on the table.`);
        // check if order already exists on the table
        if (!(await orderRow.isVisible())) {
            const orderIdLocator = magentoPlugin.page
                .locator('ul.admin__current-filters-list[data-role="filter-list"] li span[data-bind="text: preview"]:visible')
                .filter({
                    hasText: this.orderNumber,
                });

            console.log(`Checking if order with ID ${this.orderNumber} has already been searched.`);
            // verify if the order has already been searched,
            // and avoid searching for it again if it has.
            if (!(await orderIdLocator.isVisible())) {
                const searchInputLocator = magentoPlugin.helper.resolveLocator('admin.ordersTable.searchInput');
                await searchInputLocator.click();
                await searchInputLocator.fill(this.orderNumber);
                await searchInputLocator.press('Enter');
                await magentoPlugin.helper.waitToBeHidden('admin.ordersTable.loader');
            }
        }

        console.log(`Waiting for order with ID ${this.orderNumber} to appear on the table.`);
        await orderRow.waitFor({ state: 'visible', timeout: 20000 });

        if (!(await orderRow.isVisible())) {
            throw new Error(`Order with ID ${this.orderNumber} not found in Admin.`);
        }

        console.log(`Order with ID ${this.orderNumber} found in Admin.`);

        await this.navigateToOrderDetailedPage();
    }

    /**
     * Finds the latest order in the admin interface.
     */
    public async findLatestOrder(): Promise<void> {
        console.log('Finding the latest order in Admin.');
        await this.adminOrderService.navigateToAdminPage('/sales/order');
        await magentoPlugin.helper.waitToBeHidden('admin.ordersTable.loader');

        const clearAllButton = magentoPlugin.page.getByRole('button', { name: 'Clear all' });
        (await clearAllButton.isVisible())
            ? (console.log("Clicking on 'Clear all' button to remove existing filters."), await clearAllButton.click())
            : null;

        await magentoPlugin.page.waitForTimeout(5000); // Wait for the order table to update
        await magentoPlugin.page.reload();

        const latestOrder = magentoPlugin.page.locator(magentoPlugin.getSelector('admin.ordersTable.tableRows')).first();
        await latestOrder.click();

        console.log('Latest order found in Admin.');
    }

    /**
     * Verifies if the current page is the order details page.
     */
    public async isInsideDetailsPage(): Promise<boolean> {
        return (
            magentoPlugin.page.url().includes('/order/view/order_id') &&
            magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.titleHeading')(this.orderNumber)).isVisible()
        );
    }

    /**
     * Navigates to the detailed page of the current order.
     */
    public async navigateToOrderDetailedPage() {
        await magentoPlugin.page.locator(magentoPlugin.getSelector('admin.ordersTable.findById')(this.orderNumber)).click();
        await magentoPlugin.page.waitForURL(/.*order\/view\/order_id.*/, { timeout: 30000 });

        await magentoPlugin.helper.waitToBeVisible(
            magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.titleHeading')(this.orderNumber))
        );

        if (!magentoPlugin.sharedData['adminOrderUrls']) magentoPlugin.sharedData['adminOrderUrls'] = {};
        magentoPlugin.sharedData['adminOrderUrls'][this.orderNumber] = magentoPlugin.page.url();
    }

    /**
     * Navigates to the specified section of an order.
     * @param sectionName - The section to navigate to (e.g., "Invoices", "Credit Memo").
     */
    public async navigateToOrderSection(sectionName: string): Promise<void> {
        await magentoPlugin.page.getByRole('link', { name: 'View', exact: true }).click();
        await magentoPlugin.page.getByRole('link', { name: sectionName }).click();
    }

    public async navigateToInvoices() {
        console.log('Navigating to Invoices tab.');
        await magentoPlugin.helper.waitToBeVisible('admin.orderDetailed.sidebar');
        await magentoPlugin.helper.waitForVisibleAndClick('admin.tabInvoices', 'Invoices tab not found.', {
            delay: 200,
        });
        await magentoPlugin.helper.waitToBeHidden('admin.orderDetailed.invoices.loadingSpinner', { timeout: 30000 });
        await magentoPlugin.helper.waitToBeVisible('admin.orderDetailed.invoices.table', { timeout: 30000 });

        console.log('Waiting for invoices to load.');
    }

    /**
     * Navigates to the Credit Memo section of an order.
     */
    public async navigateToCreditMemo(): Promise<void> {
        expect(magentoPlugin.page.url()).toContain('order/view/order_id');

        await this.navigateToInvoices();

        await magentoPlugin.helper.waitForVisibleAndClick(
            magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.invoices.findInvoiceById')(this.orderNumber)),
            'Invoice not found.'
        );

        console.log('Navigating to invoice found for the order.');
        await magentoPlugin.helper.waitForVisibleAndClick('admin.orderDetailed.linkCreditMemo', 'Credit Memo link not found.');
        console.log('Navigating to Credit Memo.');
        expect(magentoPlugin.page.url()).toContain('sales/order_creditmemo');
        await magentoPlugin.page.waitForLoadState('networkidle');
    }

    /**
     * Navigates to the 'Ship' view and processes order shipment.
     */
    public async shipCurrentOrder(): Promise<void> {
        console.log("Navigating to the 'Ship' view to create a new shipment.");

        expect(magentoPlugin.page.url(), 'Expected to be on the order details page.').toContain('order/view/order_id');

        await magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.shipAction')).click();

        expect(magentoPlugin.page.url(), "Expected to be on 'Ship' view to create a new shipment.").toContain('order_shipment/new/order_id');

        await magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.submitShipmentButton')).click();
    }

    /**
     * Extract Buckaroo Transaction Key
     */
    public async extractBuckarooTransaction(): Promise<TransactionInfo> {
        await magentoPlugin.helper.waitToBeVisible('admin.orderDetailed.orderHistory');

        this.paidTransactions = await this.adminOrderHistoryService.fetchLatestPaymentTransaction({
            hasBckTransactionKey: true,
            findOrFail: true,
        });

        console.log(`Extracted Transaction ID: ${this.paidTransactions.bckTransactionKey}`);
        magentoPlugin.sharedData['bckTransactionKey'] = this.paidTransactions.bckTransactionKey;

        console.log(`Order Amount confirmed: ${this.paidTransactions.amount?.floatValue}`);
        return this.paidTransactions;
    }

    /**
     * Verifies the Buckaroo transaction status.
     * @param expectedStatus - The expected transaction status ('success', 'rejected', 'canceled', 'pendingProcessing', 'failed').
     */

    public async verifyBuckarooTransaction(expectedStatus: TransactionStatus = 'success'): Promise<void> {
        const paidTransaction = this.paidTransactions;

        const transactionService = new TransactionStatusService(paidTransaction.bckTransactionKey as string);
        const transactionResponse = await transactionService.status();

        const relatedTransactionKey = get(transactionResponse.data, 'relatedTransactions.0.relatedTransactionKey');

        console.log('Verifying transaction using Node SDK...');

        const getStatusCheck = (response: TransactionResponse) => {
            switch (expectedStatus) {
                case 'success':
                    return response.isSuccess();
                case 'rejected':
                    return response.isRejected();
                case 'canceled':
                    return response.isCanceled();
                case 'pendingProcessing':
                    return response.isPendingProcessing();
                case 'failed':
                    return response.isFailed();
                default:
                    throw new Error(`Unsupported transaction status: ${expectedStatus}`);
            }
        };

        if (!isNil(relatedTransactionKey)) {
            const relatedTransactionService = new TransactionStatusService(relatedTransactionKey as string);
            const relatedTransactionResponse = await relatedTransactionService.status();
            console.log(`Extracted Related Transaction ID: ${relatedTransactionResponse.getTransactionKey()}`);

            expect(getStatusCheck(relatedTransactionResponse), `Transaction's status is not '${expectedStatus}'!`).toBe(true);
            expect(relatedTransactionResponse.getAmountDebit()).toBe(paidTransaction.amount?.floatValue);
            return;
        }

        expect(getStatusCheck(transactionResponse), `Transaction's status is not '${expectedStatus}'!`).toBe(true);
        expect(
            transactionResponse.getAmountDebit(),
            `Expected transaction amount '${paidTransaction.amount?.floatValue}' to match the order amount '${transactionResponse.getAmountDebit()}'`
        ).toBe(paidTransaction.amount?.floatValue);

        console.log(`The expected transaction status '${expectedStatus}' has been verified`);
    }

    /**
     * Verifies the order status matches the expected status.
     */
    public async verifyOrderTotalPaid(): Promise<void> {
        console.log('Verifying order paid total.');
        const orderSummary = await new OrderSummaryService(magentoPlugin.getSelector('admin.orderDetailed.orderSummary')).parse();
        const expectedPaidAmount =
            (magentoPlugin.sharedData['invoice_handling'] ?? 'payment') == 'payment' ? magentoPlugin.sharedData['orderSummary']['orderTotal'] : 0;

        expect(orderSummary.paidAmount).toBe(expectedPaidAmount);
        console.log(`Order paid total: ${orderSummary.paidAmount} - Expected: ${expectedPaidAmount}`);
    }
}
