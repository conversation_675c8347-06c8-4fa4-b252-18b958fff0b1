import fs from 'fs';
import { createInterface } from 'node:readline/promises';
import { stdout } from 'node:process';

export async function ask(question: string): Promise<string> {
    const ttyPath = process.platform === 'win32' ? 'CON' : '/dev/tty';
    const input = fs.createReadStream(ttyPath, { encoding: 'utf8' });
    const rl = createInterface({ input, output: stdout });
    const answer = (await rl.question(question)).trim();
    rl.close();
    input.close();
    return answer;
}
