import CardPaymentMethod from '@core/pages/payments/methods/CardPaymentMethod';
import { CardInfo, CreditCardOptions } from '@core/pages/payments/methods/types';
import wooPlugin from '@woo/WooPlugin';

const cards: Record<string, { name: string; cardInfo: CardInfo }> = {
    amex: {
        name: 'American Express',
        cardInfo: {
            name: 'Automated Test',
            number: '***************',
            expMonth: '01',
            expYear: '29',
            cvv: '1234',
        },
    },
    visa: {
        name: 'VISA',
        cardInfo: {
            name: 'Automated Test',
            number: '****************',
            expMonth: '01',
            expYear: '27',
            cvv: '123',
        },
    },
    mastercard: {
        name: 'MasterCard',
        cardInfo: {
            name: 'Automated Test',
            number: '****************',
            expMonth: '01',
            expYear: '29',
            cvv: '123',
        },
    },
    bancontact: {
        name: 'Bancontact',
        cardInfo: {
            name: 'Automated Test',
            number: '67034200554565015',
            expMonth: '01',
            expYear: '29',
        },
    },
};

export default class CreditCardPaymentMethod extends CardPaymentMethod<CreditCardOptions> {
    /**
     * Handles actions after selecting the payment method.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        await this.selectCard();
    }

    /**
     * Handles actions after selecting the payment method with a negative scenario.
     */
    async afterNegativeSelectPaymentMethod(): Promise<void> {
        await this.selectCard();
    }

    /**
     * Clicks the "Pay Now" button to proceed with the payment.
     */
    async payNow(): Promise<void> {
        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.creditCards.payNowBtn'));
    }

    /**
     * Retrieves card details for the selected card option.
     * @returns The card details object.
     */
    protected getCardDetails(): CardInfo {
        return cards[this.options.card].cardInfo;
    }

    /**
     * Selects the card based on the provided card option.
     */
    protected async selectCard(): Promise<void> {
        const cardLocator = wooPlugin.page.locator(`text=${cards[this.options.card].name}`);
        if (cards[this.options.card].name !== cards.bancontact.name) {
            await wooPlugin.helper.clickAndExpectVisible(cardLocator, `'${cards[this.options.card].name}' is not found on buckaroo checkout`);
        }
        console.log(`Card "${this.options.card}" is selected!`);
    }
}
