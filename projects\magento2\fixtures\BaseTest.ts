import { expect, test as base } from '@core/fixtures/BaseTest';
import { AdminPlazaRefundService, AdminRefundService } from '@magento2/services/admin';
import { OrderService } from '@magento2/services/storefront';
import { CustomFixtures } from '@magento2/fixtures/types';
import { PlazaTestScenarioService } from '@core/services/buckaroo';

const test = base.extend<CustomFixtures>({
    orderService: async ({}, use): Promise<void> => {
        await use(new OrderService());
    },
    refundService: async ({}, use): Promise<void> => {
        await use(new AdminRefundService());
    },
    plazaRefundService: async ({}, use): Promise<void> => {
        await use(new AdminPlazaRefundService());
    },
    plazaTestScenarioService: async ({}, use): Promise<void> => {
        await use(new PlazaTestScenarioService());
    },
});

export { test, expect };
