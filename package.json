{"name": "qaplatform_tests", "version": "1.0.0", "description": "--- PROJECT SETUP GUIDE ---\r Welcome to the Project setup guide for the QA PLATFORM!", "main": "index.js", "type": "module", "scripts": {"test:magento2": "npx playwright test --config=projects/magento2/playwright.config.ts", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@buckaroo/buckaroo_sdk": "^1.4.2", "@types/lodash-es": "^4.17.12", "dotenv-expand": "^12.0.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "playwright": "^1.45.0"}, "devDependencies": {"@playwright/test": "^1.48.2", "@types/node": "^22.9.0", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}