import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';
import magentoPlugin from '@magento2/MagentoPlugin';
import { OrderSteps } from '@magento2/services/storefront';

test.describe('Fashion Cheque Tests', () => {
    test('Place order with Fashion Cheque (redirect)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'fashioncheque', amount: 10 },
        });
    });

    test('Place order with Fashion Cheque (redirect) - (with multiple products)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'fashioncheque', amount: 10 },
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });
    });

    test('Refund order with Fashion Cheque (redirect) - (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '1',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.GIFTCARDS,
            paymentGatewayOptions: { card: 'fashioncheque', amount: 10 },
        });

        await plazaRefundService.executeSteps({});
    });

    test('Place order with Fashion Cheque (inline)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.FASHION_CHEQUE,
            paymentGatewayOptions: { amount: 10 },
        });
    });

    test('Place order with Fashion Cheque (inline) - (with multiple products)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.FASHION_CHEQUE,
            paymentGatewayOptions: { amount: 10 },
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });
    });

    test('Refund order with Fashion Cheque (inline) - (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await new AdminBuckarooConfigPage().setConfig('giftcards', [
            {
                key: magentoPlugin.getSelector('admin.config.giftCardCheckoutOption'),
                value: '0',
                method: 'selectOption',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.FASHION_CHEQUE,
            paymentGatewayOptions: { amount: 10 },
        });

        await plazaRefundService.executeSteps({});
    });
});
