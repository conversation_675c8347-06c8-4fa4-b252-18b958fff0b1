import { PaymentGatewayOptions, ProcessFlowService } from '@core/services/workflow';
import BasePage from '@core/pages/BasePage';

export default abstract class PaymentMethod<TOptions = PaymentGatewayOptions, ICheckoutPage = BasePage> {
    protected readonly processFlowService: ProcessFlowService;

    /**
     * Constructor for the PaymentMethod class.
     * Ensures the class cannot be instantiated directly.
     * @param processFlowService
     */
    protected constructor(processFlowService: ProcessFlowService) {
        if (new.target === PaymentMethod) {
            throw new Error('Cannot instantiate abstract class PaymentMethod directly.');
        }

        this.processFlowService = processFlowService;
    }

    get checkoutPage(): ICheckoutPage {
        return this.processFlowService.pages.checkoutPage;
    }

    get serviceCode(): string | undefined {
        return this.processFlowService.options.paymentMethod;
    }

    get options(): TOptions {
        return {
            ...this.processFlowService.options,
            ...(this.processFlowService.options.paymentGatewayOptions ?? {}),
        } as TOptions;
    }
}
