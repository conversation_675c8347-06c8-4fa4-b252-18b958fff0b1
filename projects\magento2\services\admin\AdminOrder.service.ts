import { env } from '@/utils/env';
import { isEmpty } from 'lodash-es';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class AdminOrderService {
    /**
     * Navigates to the specified admin page.
     * @param path - The path to navigate to, appended to ADMIN_BASE_URL.
     * @throws Will throw an error if ADMIN_BASE_URL is not set.
     */
    public async navigateToAdminPage(path: string): Promise<void> {
        const adminBaseUrl = env('ADMIN_BASE_URL');

        if (isEmpty(adminBaseUrl)) {
            throw new Error('ADMIN_BASE_URL environment variable is not set.');
        }

        await magentoPlugin.page.goto(`${adminBaseUrl}${path}`);
        await magentoPlugin.page.waitForURL(new RegExp(`.*${path}.*`), { timeout: 5000 });
    }
}
