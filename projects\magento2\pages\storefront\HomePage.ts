// HomePage.ts
import { env } from '@/utils/env';
import magentoPlugin from '@magento2/MagentoPlugin';
import { ProductPage, StorefrontBasePage } from '@magento2/pages/storefront/index';
import { Product } from '@magento2/services/storefront';

export default class HomePage extends StorefrontBasePage {
    protected productPage: ProductPage;

    constructor() {
        super();
        this.productPage = new ProductPage();
    }

    /**
     * Navigates to a specific URL.
     * @param url - The URL to navigate to.
     */
    async navigateTo(url: string): Promise<void> {
        await magentoPlugin.page.goto(url);
    }

    /**
     * Navigates to the home page.
     */
    async goToHomePage(): Promise<void> {
        await this.navigateTo(env('BASE_URL'));
    }

    /**
     * Adds multiple items to the cart based on their IDs and quantities.
     * @param products - An array of products with their IDs and quantities.
     */
    async addItemsToCart(products: Product[] = []): Promise<void> {
        if (products.length) {
            for (const product of products) {
                await this.productPage.addSingleItemToCart(product);
            }
        } else {
            await this.productPage.addSingleItemToCart({
                id: await this.productPage.getRandomProductId(),
            });
        }
    }

    /**
     * Navigates to the cart page.
     */
    async goToCart(): Promise<void> {
        await magentoPlugin.helper.clickAndExpectVisible('storefront.product.goToCart', 'Cart button is not visible.');
    }
}
