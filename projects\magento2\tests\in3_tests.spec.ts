import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import magentoPlugin from '@magento2/MagentoPlugin';
import { OrderSteps } from '@magento2/services/storefront';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('In3 Tests', () => {
    test('Place order with In3', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'flat',
        });
    });

    test('Place order with In3 (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with In3 (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'free',
        });
        await refundService.executeSteps();
    });

    test('Partial refund order with In3 (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with In3 (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'flat',
        });
        await plazaRefundService.executeSteps();
    });

    test('Place order with In3 (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('capayablein3', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.IN3),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with In3 (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('capayablein3', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.IN3),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with In3 (Rejected)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            paymentGatewayOptions: { responseStatus: '690' },
        });
    });

    test('Place order with In3 (Cancelled by user)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });

    test('Place order with In3 (Failed)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IN3,
            paymentGatewayOptions: { responseStatus: '490' },
        });
    });
});
