import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { PlazaTestScenarioService } from 'core/services/buckaroo';

export default class TransferPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after extracting transaction.
     */
    async afterAdminExtractBckTransaction(): Promise<void> {
        await new PlazaTestScenarioService().executeSteps({ scenario: 'Paid in full' });
        await this.processFlowService.pages.adminOrderDetailsPage.setOrderNumber(magentoPlugin.sharedData['orderNumber']).findOrderByID();
    }
}
