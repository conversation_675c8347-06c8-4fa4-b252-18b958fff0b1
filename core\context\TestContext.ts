import { <PERSON><PERSON> } from '@buckaroo/buckaroo_sdk';
import { env } from '@/utils/env';
import { get, isNil, merge, set } from 'lodash-es';
import { DotNotationKeys, GetSelectorOptions, SelectorsMapType, Stage } from './TestContext.types';
import { Page } from '@playwright/test';
import Helper from '@core/services/Helper';
import generalSelectors from '@core/selectors/selectors';
import { PaymentFactory } from '@core/services/payments';

export let globalContext: TestContext;

/**
 * TestContext provides a platform-agnostic way to manage test state, access selectors,
 * interact with payment providers, and share data between test steps.
 */
export abstract class TestContext<TSelectors extends Record<string, any> = Record<string, any>> {
    public buckarooClient: Buckaroo;
    public selectors!: TSelectors;
    public sharedData: Record<string, any>;
    public page!: Page;
    public helper!: Helper;
    public static selectorsMap: SelectorsMapType = {};

    /**
     * Loads selectors dynamically based on the environment stage.
     * @throws Error if no selectors are found for the current stage.
     * @returns The selectors object for the current stage.
     */
    public loadSelectors(): TSelectors {
        const stage = env('STAGE') as Stage;

        return merge((this.constructor as typeof TestContext).selectorsMap[stage], generalSelectors);
    }

    /**
     * Creates a new instance of TestContext.
     */
    public constructor() {
        this.buckarooClient = this.initBuckarooClient();
        this.sharedData = {};

        this.selectors = this.loadSelectors();
        this.helper = new Helper<TSelectors>(this);

        globalContext = this;
    }

    /**
     * Initializes the Buckaroo client.
     * @returns An initialized Buckaroo client instance.
     */
    initBuckarooClient(): Buckaroo {
        return Buckaroo.InitializeClient(
            {
                secretKey: env('BPE_SECRET_KEY'),
                websiteKey: env('BPE_WEBSITE_KEY'),
            },
            {
                mode: env('BPE_MODE') === 'LIVE' ? 'LIVE' : 'TEST',
                currency: 'EUR',
            }
        );
    }

    /**
     * Retrieves a selector by key.
     * Supports dot-notated keys for accessing nested selectors.
     *
     * @param key - The dot-notated key of the selector.
     * @param defaultValue - Optional default value if selector is not found.
     * @param options - Optional settings for selector retrieval.
     */
    public getSelector(key: DotNotationKeys<TSelectors>, defaultValue?: string, options: GetSelectorOptions = {}): any {
        const { throwOnMissing = true, errorMessage, showWarns = false } = options;

        const selector = get(this.selectors, key);

        // Check if the selector is `null` or `undefined`.
        if (isNil(selector)) {
            const msg = errorMessage || `Selector for key "${key}" not found.`;
            if (throwOnMissing) {
                console.error(msg);
                throw new Error(msg);
            } else {
                showWarns && console.warn(msg + ' Returning default value.');
                return defaultValue ?? undefined;
            }
        }

        return selector;
    }

    /**
     * Sets the test mode for the current tests.
     */
    set testMode(value: 'negative' | 'positive') {
        this.sharedData.testMode = value;
    }

    /**
     * Gets the test mode for the current tests.
     */
    get testMode() {
        return this.sharedData.testMode ?? 'positive';
    }

    /**
     * Returns the PaymentFactory instance.
     */
    get paymentFactory() {
        return PaymentFactory;
    }

    /**
     * Sets a value in the shared data store.
     * @param key - The key to set.
     * @param value - The value to store.
     */
    setSharedData(key: string, value: any) {
        set(this.sharedData, key, value);
    }
}
