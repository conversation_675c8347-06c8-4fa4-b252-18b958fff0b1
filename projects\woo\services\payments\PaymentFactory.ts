import { PaymentMethodCode, PaymentMethods } from '@/utils/paymentMethods';
import { PaymentFactory as BasePaymentFactory, PaymentMethodConstructor } from '@core/services/payments';
import * as PaymentMethodsMap from '@woo/pages/payments';

export default class PaymentFactory extends BasePaymentFactory {
    /**
     * Registry mapping payment method keys to their respective classes.
     */
    public static readonly paymentMethodRegistry: Partial<Record<PaymentMethodCode, PaymentMethodConstructor<any>>> = {
        [PaymentMethods.IDEAL]: PaymentMethodsMap.IdealPaymentMethod,
        [PaymentMethods.IN3]: PaymentMethodsMap.In3PaymentMethod,
        [PaymentMethods.CREDITCARD_DEBITCARD]: PaymentMethodsMap.CreditCardPaymentMethod,
        [PaymentMethods.MULTIBANCO]: PaymentMethodsMap.MultibancoPaymentMethod,
        [PaymentMethods.BILLINK]: PaymentMethodsMap.BillinkPaymentMethod,
        [PaymentMethods.BELFIUS]: PaymentMethodsMap.BelfiusPaymentMethod,
        [PaymentMethods.KBC]: PaymentMethodsMap.KbcPaymentMethod,
        [PaymentMethods.SEPA]: PaymentMethodsMap.SepaPaymentMethod,
    };
}
