import { expect, Locator, Response } from '@playwright/test';
import wooPlugin from '@woo/WooPlugin';
import { StorefrontBasePage } from '@woo/pages/storefront/index';
import { isNil, isString } from 'lodash-es';
import { OrderExecuteStepsParams } from '@woo/services/storefront';
import { env } from '@/utils/env';
import { customerData } from '@core/selectors/customerData';

/**
 * Checkout-page actions, consolidated & DRY.
 */
export default class CheckoutPage extends StorefrontBasePage {
    /**
     * Navigates to the checkout page.
     */
    async navigateHere(): Promise<void> {
        await wooPlugin.page.goto(`${env('BASE_URL')}/checkout`);
    }

    /**
     * Selects a payment method by its identifier.
     * Only handles the UI interaction of selecting the payment method.
     * @param method - The identifier for the payment method.
     */
    async selectPaymentMethod(method: string): Promise<void> {
        const result = await this.selectRadioOption(
            wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.selectPaymentMethod')(method)),
            'payment',
            `Payment method '${method}' not found.`
        );

        result && (await this.waitForResponseType('checkout'));
    }

    async selectShippingMethod(methodKey: OrderExecuteStepsParams<any>['shippingMethod'] = 'free'): Promise<void> {
        await this.selectRadioOption(
            wooPlugin.page.locator(wooPlugin.getSelector(`storefront.checkout.${methodKey}ShippingMethod`)),
            'shipping',
            `Shipping method '${methodKey}' not found.`
        );
    }

    /**
     * Waits for the cart calculation AJAX request to complete.
     * This is typically needed after changing payment or shipping methods.
     */
    async placeOrder(): Promise<void> {
        await wooPlugin.helper.clickIfVisible(
            wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.placeOrderButton')),
            'Place-order button not found.'
        );
    }

    /**
     * Checks if the payment fee matches the expected value.
     * @param paymentFee
     */
    async checkPaymentFee(paymentFee: number | string): Promise<void> {
        const totals = wooPlugin.sharedData.orderSummary.totals;
        const expectedFee = parseFloat(this.calculateFee(totals, paymentFee));
        const actualFee = parseFloat(totals.fee_total);

        expect(actualFee).toEqual(expectedFee);
    }

    /**
     * Selects a radio option for payment or shipping methods.
     * @param locator
     * @param type
     * @param notFoundMessage
     * @private
     */
    private async selectRadioOption(locator: Locator, type: 'payment' | 'shipping' | 'checkout', notFoundMessage: string): Promise<boolean | void> {
        console.log(`Selecting ${type} option…`);

        await expect(locator, notFoundMessage).toBeVisible();

        if (await locator.isChecked()) {
            console.log(`${type} option already selected`);
            return false;
        }

        await Promise.all([this.waitForResponseType(type), locator.click()]);
        return true;
    }

    private async waitForResponseType(type: 'payment' | 'shipping' | 'checkout') {
        return wooPlugin.page.waitForResponse((response: Response): boolean => {
            const req = response.request();

            switch (type) {
                case 'payment':
                    return Boolean(
                        response.url().includes('/wp-admin/admin-ajax.php') &&
                            req.method() === 'POST' &&
                            req.postData()?.includes('action=woocommerce_cart_calculate_fees') &&
                            response.status() === 200
                    );
                case 'checkout':
                    return Boolean(response.url().includes('wp-json/wc/store/v1/checkout') && req.method() === 'POST' && response.status() === 200);
                case 'shipping':
                    return Boolean(
                        response.url().includes('/wp-json/wc/store/v1/cart/select-shipping-rate') &&
                            req.method() === 'POST' &&
                            response.status() === 200
                    );
            }
        });
    }

    /**
     * Calculates fee based on percentage
     */
    private calculateFee(totals: any, paymentFee: number | string): string {
        if (isString(paymentFee) && paymentFee.endsWith('%')) {
            const pct = parseFloat(paymentFee);
            if (isNaN(pct) || isNil(totals?.subtotal)) throw new Error('Invalid percentage or missing subtotal');

            const base = Number(totals.subtotal); // percentage fee
            return ((base * pct) / 100).toFixed(2);
        }

        return Number(paymentFee).toFixed(2); // fixed fee
    }

    async fillFormForGuest() {
        if (await this.isAlreadyLoggedIn()) {
            console.log('User is already logged in');
            return;
        }

        const customerFieldSelectors = wooPlugin.getSelector('storefront.checkout.customerForm') as Record<string, string>;
        await wooPlugin.page.fill(customerFieldSelectors['email'], customerData.email);
        await wooPlugin.page.fill(customerFieldSelectors['firstName'], customerData.firstName);
        await wooPlugin.page.fill(customerFieldSelectors['lastName'], customerData.lastName);
        await wooPlugin.page.fill(customerFieldSelectors['street1'], customerData.street1);
        await wooPlugin.page.fill(customerFieldSelectors['street2'], customerData.street2);
        await wooPlugin.page.selectOption(customerFieldSelectors['country'], customerData.country);
        await wooPlugin.page.fill(customerFieldSelectors['city'], customerData.city);
        await wooPlugin.page.fill(customerFieldSelectors['zip'], customerData.zip);
        await wooPlugin.page.fill(customerFieldSelectors['phone'], customerData.phone);
    }

    /**
     * Fills the date of birth field.
     * @param date - The date to enter in the field.
     */
    async fillDateOfBirth(date: string): Promise<void> {
        const dateOfBirth = wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.dateOfBirth'));
        await dateOfBirth.fill(date);
        console.log(`Entered date of birth: ${date}`);
        await dateOfBirth.press('Tab');
    }
}
