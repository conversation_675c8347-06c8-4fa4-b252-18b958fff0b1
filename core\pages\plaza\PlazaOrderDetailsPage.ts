import BasePage from '@core/pages/BasePage';
import { PlazaService } from '@core/services/buckaroo';
import { globalContext } from '@core/context/TestContext';

export default class PlazaOrderDetailsPage extends BasePage {
    public readonly plazaService: PlazaService;

    constructor() {
        super();
        this.plazaService = new PlazaService();
    }

    /**
     * Navigates to the Transaction Detail page.
     * @param bckTransactionKey - Transaction Key.
     */
    public async navigateToTransaction(bckTransactionKey: string): Promise<void> {
        return await this.plazaService.navigateToTransaction(bckTransactionKey);
    }

    /**
     * Retrieves the order number from the page.
     */
    public async getOrderNumber(): Promise<string | null> {
        try {
            const orderNumberLocator = globalContext.page
                .locator('tr')
                .filter({ has: globalContext.page.getByRole('cell', { name: 'Invoice number:' }) })
                .locator('td span');
            const orderNumber = await globalContext.helper.getText(orderNumberLocator);
            return orderNumber || null;
        } catch (error) {
            console.error('Error retrieving order number:', error);
            return null;
        }
    }
}
