import { MagentoSelectorsMapType, Stage } from '@magento2/selectors';
import { TestContext } from '@core/context/TestContext';
import { SelectorsMapType } from '@core/context/TestContext.types';
import selectors_m245 from '@magento2/selectors/selectors_m245';
import selectors_m246 from '@magento2/selectors/selectors_m246';
import selectors_m247 from '@magento2/selectors/selectors_m247';
import selectors_m248 from '@magento2/selectors/selectors_m248';
import selectors_refactor from '@magento2/selectors/selectors_refactor';
import { PaymentFactory } from '@magento2/services/payments';

export class MagentoPlugin extends TestContext<MagentoSelectorsMapType[Stage]> {
    public static selectorsMap: SelectorsMapType = {
        m245: selectors_m245,
        m246: selectors_m246,
        m247: selectors_m247,
        m248: selectors_m248,
        refactor: selectors_refactor,
    };

    /**
     * Returns the PaymentFactory instance.
     */
    get paymentFactory() {
        return PaymentFactory;
    }
}

// Initialize with a specific context ID
const magentoPlugin = new MagentoPlugin();

export default magentoPlugin;
