import { Page } from '@playwright/test';
import { PlazaLoginPage, PlazaOrderDetailsPage, PlazaPerformScenarioPage, PlazaRefundPage } from 'core/pages/plaza';
import { ExecuteStepsParams } from '@core/services/workflow';

export enum PlazaRefundSteps {
    PlazaLogin = 'plazaLogin',
    GoToTransactionPage = 'goToTransactionPage',
    ProcessRefund = 'processRefund',
    VerifyRefund = 'verifyRefund',
}

export type PlazaRefundPlacementPages = {
    page: Page;
    plazaLoginPage: PlazaLoginPage;
    plazaOrderDetailsPage: PlazaOrderDetailsPage;
    plazaRefundPage: PlazaRefundPage;
};

export interface PlazaTestScenarioExecuteStepsParams extends ExecuteStepsParams<PlazaTestScenarioSteps> {
    scenario: string;
}

export enum PlazaTestScenarioSteps {
    PlazaLogin = 'plazaLogin',
    GoToTransactionPage = 'goToTransactionPage',
    ProcessPerformScenario = 'processScenario',
    VerifyPerformedScenario = 'verifyPerformedScenario',
}

export type PlazaTestScenarioPlacementPages = {
    page: Page;
    plazaLoginPage: PlazaLoginPage;
    plazaOrderDetailsPage: PlazaOrderDetailsPage;
    plazaPerformScenarioPage: PlazaPerformScenarioPage;
};
