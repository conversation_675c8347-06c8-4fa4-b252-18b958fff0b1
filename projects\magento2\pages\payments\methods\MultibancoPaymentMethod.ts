import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';

export default class MultibancoPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.proceedThroughNextSteps(2);
    }

    /**
     * Proceeds through the "Next Step" process a specified number of times.
     * @param steps - The number of times to click the "Next Step" button.
     */
    protected async proceedThroughNextSteps(steps: number): Promise<void> {
        for (let i = 0; i < steps; i++) {
            await magentoPlugin.page.click(magentoPlugin.getSelector('storefront.checkout.nextStepWallet'));
            await magentoPlugin.page.waitForLoadState('networkidle');
        }
    }
}
