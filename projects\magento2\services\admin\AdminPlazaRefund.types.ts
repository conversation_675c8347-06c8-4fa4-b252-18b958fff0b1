import { AdminLoginPage, AdminOrderDetailsPage, AdminRefundPage } from '@magento2/pages/admin';
import { PlazaRefundPlacementPages } from '@core/services/buckaroo';

export enum AdminPlazaRefundSteps {
    AdminLogin = 'adminLogin',
    GoToOrder = 'goToOrder',
    VerifyRefundOnAdmin = 'verifyRefundOnAdmin',
}

export type AdminPlazaRefundPlacementPages = PlazaRefundPlacementPages & {
    adminLoginPage: AdminLoginPage;
    adminOrderDetailsPage: AdminOrderDetailsPage;
    adminRefundPage: AdminRefundPage;
};
