name: Code Quality Checks

on: [push]

jobs:
    quality-checks:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v3

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '18.x'
                  cache: 'npm'

            - name: Install dependencies
              run: npm ci

            - name: Run TypeScript type check
              run: npm run type-check

            - name: Check code formatting
              run: npx prettier --check "**/*.{ts,js,json,yml,yaml,md}"
