import { Step } from '@core/services/workflow/ProcessFlow.types';
import { PlazaRefundService, PlazaRefundSteps as BasePlazaRefundSteps } from '@core/services/buckaroo';
import { AdminPlazaRefundPlacementPages, AdminPlazaRefundSteps } from '@woo/services/admin/AdminPlazaRefund.types';
import { AdminOrderDetailsPage } from '@woo/pages/admin';
import { AuthApiService } from '@woo/services/api';
import wooPlugin from '@woo/WooPlugin';

export default class AdminPlazaRefundService extends PlazaRefundService<
    BasePlazaRefundSteps | AdminPlazaRefundSteps,
    AdminPlazaRefundPlacementPages
> {
    constructor() {
        super();

        this.pages.adminOrderDetailsPage = new AdminOrderDetailsPage();
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<BasePlazaRefundSteps | AdminPlazaRefundSteps>[] {
        return [
            ...super.defineSteps(),
            {
                name: AdminPlazaRefundSteps.AdminLogin,
                action: async () => {
                    await new AuthApiService().login();
                },
            },
            {
                name: AdminPlazaRefundSteps.GoToOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.setOrderNumber(wooPlugin.sharedData['orderNumber']).navigateHere();
                },
            },
            {
                name: AdminPlazaRefundSteps.VerifyRefundOnAdmin,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.verifyPlazaRefundSuccess();
                },
            },
        ];
    }
}
