import { PaymentMethod } from '@magento2/services/payments';

export default class AlipayPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.completeWalletSteps();
    }

    /**
     * Completes the wallet steps specific to <PERSON><PERSON><PERSON>.
     */
    protected async completeWalletSteps(): Promise<void> {
        await this.checkoutPage.completeWalletSteps();
        console.log('Alipay wallet steps completed.');
    }
}
