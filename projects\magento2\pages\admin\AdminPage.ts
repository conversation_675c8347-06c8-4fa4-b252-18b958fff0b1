import BasePage from '@core/pages/BasePage';
import magentoPlugin from '@magento2/MagentoPlugin';
import { isFunction } from 'lodash-es';
import { AdminLoginPage } from '@magento2/pages/admin/index';
import { ConfigValue, NavigationStep } from '@magento2/pages/admin/types';
import { MagentoSelectorsMapType, Stage } from '@magento2/selectors';

export default class AdminPage extends BasePage {
    protected readonly adminLoginPage: AdminLoginPage;

    constructor() {
        super();
        this.adminLoginPage = new AdminLoginPage();
    }

    /**
     * Log in to admin panel.
     */
    public async loginToAdmin(): Promise<void> {
        console.log('Logging into admin...');
        await this.adminLoginPage.login();
        console.log('Logged in.');
    }

    /**
     * Getter for admin selectors.
     * Centralizes access to magentoPlugin.selectors.admin.
     */
    public get adminSelectors(): MagentoSelectorsMapType[Stage]['admin'] {
        return magentoPlugin.selectors.admin;
    }

    /**
     * Navigates through the admin sidebar menus using data-ui-id attributes.
     * @param uiIds - An array of data-ui-id strings representing the menu path.
     */
    async navigateSidebarByUiIds(uiIds: string[]): Promise<void> {
        for (const uiId of uiIds) {
            const locator = magentoPlugin.page.locator(uiId);

            await locator.hover();

            await magentoPlugin.helper.waitForVisibleAndClick(locator, `Failed to click on menu item with ui-id: ${uiId}`);
        }
    }

    /**
     * Navigates to a configuration section using titles or href parts.
     */
    public async navigateToConfigSections(steps: NavigationStep[]): Promise<void> {
        for (const step of steps) {
            let selector: string;
            if (step.section) {
                selector = step.section;
            } else if (step.hrefPart) {
                selector = `a[href*="${step.hrefPart}"]`;
            } else if (step.title) {
                selector = `a:has-text("${step.title}")`;
            } else {
                throw new Error('Navigation step must have either a title or an hrefPart.');
            }
            await magentoPlugin.page.click(selector);
            await magentoPlugin.page.waitForLoadState('networkidle');
        }
    }

    /**
     * Sets multiple configuration keys to their provided values.
     * @param configValues Array of configuration key-value pairs.
     */
    public async setConfigValues(configValues: ConfigValue[]): Promise<void> {
        for (const config of configValues) {
            if (isFunction(config)) {
                console.log(`Executing provided custom function.`);
                await config(magentoPlugin.page);
            } else {
                console.log(`Setting configuration key: ${config.key} to value: ${config.value}`);
                const configLocator = magentoPlugin.page.locator(config.key);
                if (await configLocator.isVisible()) {
                    await (configLocator[config.method] as (arg: any) => Promise<void>)(config.value);
                } else {
                    throw new Error(`Configuration key locator ${config.key} is not visible.`);
                }
            }
        }
    }

    /**
     * Saves the current configuration.
     */
    public async saveConfig(): Promise<void> {
        console.log('Saving configuration...');
        await magentoPlugin.page.locator(this.adminSelectors.config.submitButton).click();
    }

    /**
     * Verifies that the success message contains the expected text.
     * @param expectedMessage The expected success message text.
     */
    public async verifySaveSuccessMessage(expectedMessage: string): Promise<void> {
        console.log('Verifying success message...');
        const successMessageLocator = this.adminSelectors.config.messagesSuccess;
        await magentoPlugin.helper.waitToBeVisible(successMessageLocator, { timeout: 15000 });
        const successText = await magentoPlugin.helper.getText(successMessageLocator);

        if (successText?.includes(expectedMessage)) {
            console.log('Configuration saved successfully!');
        } else {
            throw new Error("Failed to save configuration. The expected message 'You saved the configuration.' was not displayed");
        }
    }

    /**
     * Navigate to store configurations.
     */
    protected async navigateToStoreConfigs(): Promise<void> {
        console.log('Navigating to store configs...');
        await this.navigateSidebarByUiIds([this.adminSelectors.sidebarStoresMenu, this.adminSelectors.sidebarConfigurationMenu]);
        await this.navigateToConfigSections([{ hrefPart: '/admin/system_config/edit/section/currency' }]);
        console.log('Reached store configs.');
    }

    /**
     * Navigate to payment methods configuration.
     */
    protected async navigateToPaymentMethodsConfig(): Promise<void> {
        console.log('Navigating to payment methods config...');
        await this.navigateToStoreConfigs();
        await this.navigateToConfigSections([{ section: this.adminSelectors.tabSales }, { hrefPart: this.adminSelectors.sidebarPaymentMenu }]);
        console.log('Reached payment methods config.');
    }

    /**
     * Open the configuration section for a payment method.
     * @param paymentMethod - Identifier of the payment method.
     */
    protected async openPaymentMethodConfigSection(paymentMethod: string): Promise<void> {
        console.log(`Opening config for: ${paymentMethod}`);
        if (paymentMethod.includes('klarna')) {
            await this.navigateToConfigSections([
                { section: this.adminSelectors.config.paymentMethodsConfigureButton },
                { section: this.adminSelectors.config.paymentMethodAccordion('klarna') },
                { section: this.adminSelectors.config.klarnaVariantPaymentMethodAccordion(paymentMethod) },
                { section: this.adminSelectors.config.paymentMethodAdvancedAccordion(paymentMethod) },
            ]);
        } else {
            await this.navigateToConfigSections([
                { section: this.adminSelectors.config.paymentMethodsConfigureButton },
                { section: this.adminSelectors.config.paymentMethodAccordion(paymentMethod) },
                { section: this.adminSelectors.config.paymentMethodAdvancedAccordion(paymentMethod) },
            ]);
        }
        console.log(`Config section for ${paymentMethod} opened.`);
    }
}
