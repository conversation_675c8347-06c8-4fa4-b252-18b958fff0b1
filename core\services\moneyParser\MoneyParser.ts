import { currencies as defaultCurrencies } from './currencies';
import { Currency, Format, ParseOptions, ParseResult } from './types';
import { Locator } from '@playwright/test';
import { isString } from 'lodash-es';
import { TestContext } from '@core/context/TestContext';

/**
 * MoneyParser class to parse and format monetary values.
 */
export default class MoneyParser {
    private currencies: Currency[];
    private priceRegex: RegExp;
    private readonly minuses: string = '[−–‑—‒-]';
    private readonly delimiters: string = `[\\s.,\\'‘’‛′´\`]`;
    private currentFormat: Format | null = null; // To store the last used format

    protected context: TestContext;

    /**
     * Constructs the MoneyParser.
     * Automatically initializes with default currencies.
     */
    constructor(context: TestContext) {
        this.context = context;
        this.currencies = defaultCurrencies;
        this.priceRegex = this.buildRegex();
    }

    /**
     * Parses the first monetary value found in the input text.
     * @param text The input string to parse.
     * @param options Parsing options.
     * @returns A single ParseResult object or null if none found.
     */
    public async parse(text: Locator | string, options: ParseOptions = {}): Promise<ParseResult | null> {
        const newText = isString(text) ? text : await this.context.helper.getText(text);
        const normalizedText = String(newText || '').trim();

        if (!normalizedText) {
            return null;
        }

        this.priceRegex.lastIndex = 0;
        const match = this.priceRegex.exec(normalizedText);

        return match ? this.matchResultToPrice(match, options) : null;
    }

    /**
     * Builds the comprehensive regex used for parsing.
     * @returns The constructed RegExp object.
     */
    private buildRegex(): RegExp {
        // Extract and escape all currency symbols and codes
        const allSymbols = this.currencies
            .reduce<string[]>((syms, curr) => syms.concat(curr.code, curr.symbols), [])
            .map(sym => sym.replace(/([\$\.])/g, '\\$1'))
            .join('|');

        const symbolGroup = `(${allSymbols})`;
        const numGroup = `(\\s*\\d+(?:${this.delimiters}*\\d+)*)`;

        const leadingSym = [this.minuses + '?', symbolGroup, this.minuses + '?', numGroup].join('\\s*');

        const trailingSym = [this.minuses + '?', numGroup, symbolGroup].join('\\s*');

        const mainGroups = `(?:\\s*${leadingSym}|\\s*${trailingSym})`;

        const startDelimiter = '(?:^|\\s+)';
        const endDelimiter = `(?=$|\\s+|[,;:\\!\\?\\.\\'"\\+\\-])`;

        const fullRegex = `${startDelimiter}${mainGroups}${endDelimiter}`;

        return new RegExp(fullRegex, 'ig');
    }

    /**
     * Converts a regex match into a ParseResult object.
     * @param match The regex match array.
     * @param options Parsing options.
     * @returns A ParseResult object or null if parsing fails.
     */
    private matchResultToPrice(match: RegExpExecArray, options: ParseOptions = {}): ParseResult | null {
        const { parseNegative = false } = options;

        // Determine if the match has a negative sign
        const isNegative = new RegExp(this.minuses).test(match[0]);

        // Extract the symbol from the appropriate capture group
        const symbol = (match[1] || match[4] || '').toLowerCase();

        // Find the corresponding currency
        const currency = this.currencies.find(cur => cur.code.toLowerCase() === symbol || cur.symbols.map(s => s.toLowerCase()).includes(symbol));

        if (!currency) {
            console.warn(`Currency could not be detected in match: "${match[0]}"`);
            return null;
        }

        const exponent = currency.exponent;
        const sign = parseNegative && isNegative ? -1 : 1;

        // Extract the numeric part from the appropriate capture group
        const numericPart = match[2] || match[3];
        const floatValue = sign * this.parseNumber(numericPart, exponent);
        const value = Math.round(floatValue * Math.pow(10, exponent));

        // Determine the format
        const format = this.determineFormat(match, symbol);

        // Save the format
        this.currentFormat = format;

        return {
            value,
            floatValue,
            symbol,
            currencyCode: currency.code,
            currency,
            format,
        };
    }

    /**
     * Determines the format used in the matched string.
     * @param match The regex match array.
     * @param symbol The currency symbol detected.
     * @returns A Format object representing the detected format.
     */
    private determineFormat(match: RegExpExecArray, symbol: string): Format {
        const rawMatch = match[0].trim();

        // Determine symbol position: prefix or suffix
        const symbolAtStart = rawMatch.startsWith(symbol);
        const symbolPosition: 'prefix' | 'suffix' = symbolAtStart ? 'prefix' : 'suffix';

        // Determine negative sign used
        const negativeMatch = rawMatch.match(new RegExp(this.minuses));
        const negativeSign = negativeMatch ? negativeMatch[0] : '-';

        // Determine separators by analyzing the numeric part
        const numericPart = match[2] || match[3];
        let decimalSeparator = '.';
        let thousandSeparator = ',';

        // Heuristics to determine separators
        const delimiterMatches = numericPart.match(new RegExp(this.delimiters, 'g'));
        if (delimiterMatches) {
            // Assume the last delimiter is the decimal separator
            const lastDelimiter = delimiterMatches[delimiterMatches.length - 1];
            if (lastDelimiter === '.' || lastDelimiter === ',') {
                decimalSeparator = lastDelimiter;
                // The thousand separator is the other one
                const otherDelimiters = delimiterMatches.filter(d => d !== decimalSeparator);
                thousandSeparator = otherDelimiters.length > 0 ? otherDelimiters[0] : ',';
            } else {
                // Default separators if unable to determine
                decimalSeparator = '.';
                thousandSeparator = ',';
            }
        }

        return {
            currencySymbol: symbol,
            currencyCode: symbol, // Ideally, use currency.code instead
            symbolPosition,
            decimalSeparator,
            thousandSeparator,
            negativeSign,
        };
    }

    /**
     * Formats a numerical amount into a monetary string using the saved format.
     * @param amount The numerical amount to format.
     * @param options Formatting options (optional).
     * @returns The formatted monetary string.
     */
    public formatAmount(amount: number, options: { useSavedFormat?: boolean } = {}): string {
        const { useSavedFormat = true } = options;

        let format: Format | null = null;

        if (useSavedFormat) {
            if (!this.currentFormat) {
                throw new Error('No format available. Please parse a monetary string first or provide a format.');
            }
            format = this.currentFormat;
        } else {
            throw new Error('No alternative formats implemented yet.');
            // Future enhancement: allow passing a specific format
        }

        const { currencySymbol, symbolPosition, decimalSeparator, thousandSeparator, negativeSign } = format;

        const isNegative = amount < 0;
        const absoluteAmount = Math.abs(amount);

        // Split the amount into integer and decimal parts
        // Use the exponent to determine decimal places
        // Assuming exponent is stored in the format or can be derived
        // Here, defaulting to 2 decimal places
        const decimalPlaces = 2;
        const [integerPart, decimalPart] = absoluteAmount.toFixed(decimalPlaces).split('.');

        // Add thousand separators
        const integerWithThousand = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

        // Combine integer and decimal parts
        const formattedNumber = decimalSeparator ? `${integerWithThousand}${decimalSeparator}${decimalPart}` : integerWithThousand;

        // Add currency symbol
        const withSymbol = symbolPosition === 'prefix' ? `${currencySymbol}${formattedNumber}` : `${formattedNumber} ${currencySymbol}`;

        // Add negative sign if necessary
        return isNegative ? `${negativeSign}${withSymbol}` : withSymbol;
    }

    /**
     * Parses the numeric string into a floating-point number based on the currency's exponent.
     * @param str The numeric string extracted from the input.
     * @param exponent The number of decimal places for the currency.
     * @returns The parsed floating-point number.
     */
    private parseNumber(str: string, exponent: number): number {
        const chunks = str.split(new RegExp(this.delimiters + '+'));
        const num = parseInt(chunks.join(''), 10);
        const lastChunk = chunks[chunks.length - 1];
        const hasDecimalPoint = chunks.length > 1 && lastChunk.length <= exponent;
        const actualExponent = hasDecimalPoint ? lastChunk.length : 0;

        return hasDecimalPoint ? num / Math.pow(10, actualExponent) : num;
    }
}
