import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('PayByBank Tests', () => {
    test('Place order with PayByBank', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
        });
    });

    test('Place order with PayByBank (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with PayByBank (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with PayByBank (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with PayByBank (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with PayByBank (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('paybybank', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.PAYBYBANK),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with PayByBank (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('paybybank', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.PAYBYBANK),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PAYBYBANK,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
