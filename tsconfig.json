{
    "compilerOptions": {
        /* Base Configuration */
        "target": "ESNext" /* Specify ECMAScript target version */,
        "module": "ESNext" /* Specify module code generation */,
        "lib": ["ESNext", "DOM"] /* Specify library files to be included in the compilation */,
        "baseUrl": "." /* Base directory to resolve non-absolute module names */,
        "moduleResolution": "Node" /* How to resolve modules */,
        "esModuleInterop": true /* Enables interoperability between CommonJS and ES Modules */,
        "resolveJsonModule": true /* Include modules imported with .json extension */,

        /* Path Mapping */
        "paths": {
            "@magento2/*": ["./projects/magento2/*"],
            "@woo/*": ["./projects/woo/*"],
            "@core/*": ["./core/*"],
            "@/*": ["./*"]
        },

        /* Type Checking */
        "strict": true /* Enable all strict type-checking options */,
        "noImplicitAny": true /* Raise error on expressions and declarations with an implied 'any' type */,
        "strictNullChecks": true /* Enable strict null checks */,
        "strictFunctionTypes": true /* Enable strict checking of function types */,
        "strictBindCallApply": true /* Enable strict 'bind', 'call', and 'apply' methods on functions */,
        "strictPropertyInitialization": true /* Ensure non-undefined class properties are initialized in the constructor */,

        /* Additional Checks */
        "noUnusedLocals": true /* Report errors on unused locals */,
        "noUnusedParameters": true /* Report errors on unused parameters */,
        "noImplicitReturns": true /* Report error when not all code paths in function return a value */,
        "noFallthroughCasesInSwitch": true /* Report errors for fallthrough cases in switch statement */,

        /* Advanced Options */
        "skipLibCheck": true /* Skip type checking of declaration files */,
        "forceConsistentCasingInFileNames": true /* Ensure correct casing in import statements */,
        "allowSyntheticDefaultImports": true /* Allow default imports from modules with no default export */,
        "useUnknownInCatchVariables": true /* Type catch clause variables as 'unknown' instead of 'any' */,

        /* Type Definitions */
        "types": ["node", "@playwright/test"] /* Type declaration files to be included in compilation */
    },
    "include": ["./**/*.ts", "./**/*.tsx", "./**/*.js", "./**/*.d.ts"],
    "exclude": ["node_modules"]
}
