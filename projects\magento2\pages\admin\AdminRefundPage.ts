import BasePage from '@core/pages/BasePage';
import { expect } from '@magento2/fixtures/BaseTest';
import { AdminOrderDetailsPage } from '@magento2/pages/admin/index';
import magentoPlugin from '@magento2/MagentoPlugin';
import { TransactionStatusService } from 'core/services/buckaroo';
import { AdminOrderHistoryService } from '@magento2/services/admin';
import { PlazaRefundPage } from 'core/pages/plaza';
import { TransactionFilter } from '@magento2/services/admin/AdminOrderHistory.types';

export default class AdminRefundPage extends BasePage {
    public readonly adminOrderDetailsPage: AdminOrderDetailsPage;
    public readonly adminOrderHistoryService: AdminOrderHistoryService;
    public readonly plazaRefundPage: PlazaRefundPage;

    constructor() {
        super();
        this.adminOrderDetailsPage = new AdminOrderDetailsPage();
        this.adminOrderHistoryService = new AdminOrderHistoryService();
        this.plazaRefundPage = new PlazaRefundPage();
    }

    /**
     * Processes the refund by clicking the refund button and verifying that the order is closed.
     */
    public async processFullyRefund(): Promise<void> {
        console.log('Clicking refund button');
        await magentoPlugin.page.getByRole('button', { name: 'Refund', exact: true }).click({ timeout: 20000 });
        console.log('Verifying order status to be "Closed"');
        await this.adminOrderDetailsPage.verifyOrderStatus('Closed');
        console.log('Refund Processed Successfully');
    }

    /**
     * Processes the refund by clicking the refund button and verifying that the order is closed.
     */
    public async processPartialRefund(expectedOrderStatus: string): Promise<void> {
        const tableRowsLocator = magentoPlugin.helper.resolveLocator('admin.orderDetailed.invoices.refundOrderItems');
        const tableRows = await tableRowsLocator.all();

        expect(tableRows.length).toBeGreaterThan(0);
        console.log(`Found ${tableRows.length} refundable items`);

        let partialRefundApplied = false;

        console.log('Going through the refundable items');
        for (const row of tableRows) {
            const qtyInput = row.locator('.qty-input');
            const qtyInputValue = await qtyInput.inputValue();
            const qty = parseInt(qtyInputValue, 10);

            if (qty > 1) {
                // fill the first input with 1 and others with 0. This is to ensure that we're refunding only one item.
                const newQuantity = partialRefundApplied ? 0 : 1;
                await qtyInput.fill(String(newQuantity));
                await qtyInput.press('Tab');
                partialRefundApplied = true;
                break;
            }
        }

        if (!partialRefundApplied) {
            throw new Error('No Partial Refundable Items Found');
        }

        console.log('Clicking refresh qty button');
        await magentoPlugin.helper.resolveLocator('admin.orderDetailed.invoices.refundUpdateQty').click();
        console.log('Clicking refund button');
        await magentoPlugin.page.getByRole('button', { name: 'Refund', exact: true }).click();
        console.log(`Verifying order status to be "${expectedOrderStatus}"`);
        await this.adminOrderDetailsPage.verifyOrderStatus(expectedOrderStatus);
    }

    /**
     * Verifies that the refund is reflected on the customer side (Plaza),
     * ensuring the order status is updated and the refunded amount matches the expected value.
     */
    public async verifyPlazaRefundSuccess(expectedOrderStatus: string = 'Processing'): Promise<void> {
        await this.adminOrderDetailsPage.verifyOrderStatus(expectedOrderStatus);

        await magentoPlugin.helper.executeWithRetry(
            async () => {
                const refundedAmount = await this.getRefundedAmount();

                expect(refundedAmount, `Expected refund amount to be '${this.plazaRefundPage.refundAmount}', but got '${refundedAmount}'`).toBe(
                    parseFloat(this.plazaRefundPage.refundAmount)
                );
                console.log(`Refunded Amount: ${refundedAmount}`);
            },
            {
                retries: 10,
                delay: 5000,
                errorMessage: 'Failed to verify the refunded amount after 5 attempts.',
                logMessage: 'Verifying the refunded amount',
            }
        );
    }

    /**
     * Retrieves the latest refund transaction amount from the admin order history.
     * Throws an error if no refund transaction is found or if the amount is missing.
     */
    public async getRefundedAmount(): Promise<number> {
        const filters: TransactionFilter & { findOrFail: true } = { findOrFail: true };

        if (magentoPlugin.sharedData['plazaRefundDate']) {
            filters.fromDate = magentoPlugin.sharedData['plazaRefundDate'];
            console.log(`Filtering refund transactions from date: ${filters.fromDate}`);
        }

        const refundTransaction = await this.adminOrderHistoryService.fetchLatestRefundTransaction(filters);

        const refundedAmount = refundTransaction.amount?.floatValue;
        if (!refundedAmount) {
            throw new Error('Refund amount not found.');
        }

        console.log(`Refunded Amount: ${refundedAmount}`);

        return refundedAmount;
    }

    /**
     * Verifies the refund transaction by checking that the order is closed,
     * confirming the refund transaction details (amount, transaction ID),
     * and validating the transaction against the external service.
     */
    public async verifyRefund(expectedStatus: string): Promise<void> {
        await this.adminOrderDetailsPage.verifyOrderStatus(expectedStatus);

        const refundTransaction = await this.adminOrderHistoryService.fetchLatestRefundTransaction({
            findOrFail: true,
        });

        const { bckTransactionKey, amount } = refundTransaction;
        const refundedAmount = amount?.floatValue;
        if (!refundedAmount) {
            throw new Error('Refund amount not found.');
        }

        if (!bckTransactionKey) {
            throw new Error('Refund transaction ID not found.');
        }

        console.log(`Refunded Amount: ${refundedAmount}`);
        console.log(`Transaction ID: ${bckTransactionKey}`);

        const transactionService = new TransactionStatusService(bckTransactionKey);
        const transactionResponse = await transactionService.status();

        expect(transactionResponse.isSuccess()).toBe(true);
        expect(transactionResponse.getAmountCredit()).toBe(refundedAmount);
        console.log('The Transaction is Successful.');
    }
}
