import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';
import { OrderApiService } from '@woo/services/api';
import { expect } from '@woo/fixtures/BaseTest';
import AdminOrderService from '@woo/services/admin/AdminOrder.service';
import { PlazaService } from 'core/services/buckaroo';
import { PlazaLoginPage } from 'core/pages/plaza';

export default class SepaPaymentMethod extends PaymentMethod {
    async beforePlaceOrder(): Promise<void> {
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountHolder'), 'Test Acceptatie');
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountNumber'), '******************');

        const bicInput = wooPlugin.page.locator(wooPlugin.getSelector('paymentMethods.sepa.bicNumberInput'));
        if (await bicInput.isVisible()) {
            await bicInput.fill('TESTNL2A');
        }
    }

    async onAdminVerifyOrderStatus(): Promise<void> {
        const orderData = await new OrderApiService().getOrderStatus(wooPlugin.sharedData['orderNumber']);

        expect(['on-hold', 'processing']).toContain(orderData.status);
        expect(orderData.amounts?.total).toEqual(wooPlugin.sharedData['orderSummary']['totals']['total']);

        await this.executeSepaPlazaScenario();
    }

    async onBuckarooTransactionVerify(): Promise<void> {
        if (!wooPlugin.sharedData['bckTransactionKey']) return;

        const plazaTransactionKey = wooPlugin.sharedData['bckTransactionKey'];
        await new AdminOrderService().verifyBuckarooTransaction(wooPlugin.sharedData['orderNumber']);
        wooPlugin.sharedData['bckTransactionKey'] = plazaTransactionKey;
    }

    private async executeSepaPlazaScenario(): Promise<void> {
        await new PlazaLoginPage().login();
        await new PlazaService().navigateToAdminPage('/Transaction/Transactions/Index');
        await this.retrySearchAndClickFirstRow(wooPlugin.sharedData['orderNumber']);
    }

    private async setupSearchCriteria(orderNumber: string): Promise<void> {
        const filterButton = wooPlugin.page.locator('#transaction_list > div > div.ibox-title > button');
        if (await filterButton.isVisible()) await filterButton.click();

        await wooPlugin.page.locator('#search').fill(orderNumber);

        const transactionTypeInput = wooPlugin.page.locator('#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(3) > div > span > span.selection > span > ul > li > input');
        await transactionTypeInput.click();
        await transactionTypeInput.fill('C004 SEPA Direct Debit');

        const sepaOption = wooPlugin.page.locator('li:has-text("C004 SEPA Direct Debit")').first();
        if (await sepaOption.isVisible()) {
            await sepaOption.click();
        } else {
            await transactionTypeInput.press('Enter');
        }

        // Set today's date last - click on date field and select today
        await wooPlugin.page.locator('#datetime1').click();
        const todayButton = wooPlugin.page.locator('.k-link.k-nav-today');
        if (await todayButton.isVisible()) {
            await todayButton.click();
        } else {
            // Fallback: try to find today's date in the calendar
            const today = new Date().getDate().toString();
            const todayCell = wooPlugin.page.locator(`.k-calendar td[role="gridcell"]:has-text("${today}"):not(.k-other-month)`).first();
            if (await todayCell.isVisible()) {
                await todayCell.click();
            }
        }
    }

    private async clickSearchButton(): Promise<void> {
        const searchButton = wooPlugin.page.locator('#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(12) > div > button');
        if (await searchButton.isVisible()) {
            await searchButton.click();
        } else {
            await wooPlugin.page.locator('#search').press('Enter');
        }
    }



    private async retrySearchAndClickFirstRow(orderNumber: string): Promise<void> {
        await this.setupSearchCriteria(orderNumber);

        for (let attempt = 1; attempt <= 5; attempt++) {
            await this.clickSearchButton();
            await wooPlugin.page.waitForTimeout(5000);

            const rows = await wooPlugin.page.locator('table tbody tr').count();
            if (rows > 0) {
                const matchingRow = await this.findTransactionRow(orderNumber);
                const targetRow = matchingRow || wooPlugin.page.locator('table tbody tr').first();

                await targetRow.click();
                await this.extractTransactionKey();
                await this.executeScenarioOnCurrentPage();
                return;
            }
        }
        throw new Error('Failed to find transaction results after 5 attempts');
    }

    private async findTransactionRow(orderNumber: string): Promise<any> {
        const allRows = await wooPlugin.page.locator('table tbody tr').all();
        for (const row of allRows) {
            const rowText = await row.textContent();
            if (rowText?.includes(orderNumber)) return row;
        }
        return null;
    }

    private async extractTransactionKey(): Promise<void> {
        const currentUrl = wooPlugin.page.url();
        const transactionKeyMatch = currentUrl.match(/transactionKey=([^&]+)/);

        if (transactionKeyMatch) {
            wooPlugin.sharedData['bckTransactionKey'] = transactionKeyMatch[1];
            console.log(`Extracted Transaction Key: ${transactionKeyMatch[1]}`);
        }
    }

    private async executeScenarioOnCurrentPage(): Promise<void> {
        await wooPlugin.page.locator('.btn.btn-default.btn-sm.dropdown-toggle').click();
        await wooPlugin.page.locator('[title="Test scenarios"]').click();

        const liLocator = wooPlugin.page.locator('#testCasePanelbar li:has(a:has-text("Direct debit succeeds"))');
        await liLocator.locator('> a').click();
        await liLocator.locator('div.k-content').waitFor({ state: 'visible' });
        await liLocator.locator('input[type="button"][value="Perform scenario"]').click();

        const successMessage = wooPlugin.page.locator('#testCaseSuccessMessage');
        await successMessage.waitFor({ state: 'visible', timeout: 30000 });

        const successText = await successMessage.textContent();
        if (!successText?.includes('Successfully performed scenario')) {
            throw new Error(`Unexpected success message: ${successText}`);
        }
    }
}
