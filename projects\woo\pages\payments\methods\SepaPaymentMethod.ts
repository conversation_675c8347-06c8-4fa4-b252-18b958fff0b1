import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';
import { OrderApiService } from '@woo/services/api';
import { expect } from '@woo/fixtures/BaseTest';
import AdminOrderService from '@woo/services/admin/AdminOrder.service';
import { PlazaService } from 'core/services/buckaroo';
import { PlazaLoginPage } from 'core/pages/plaza';
import selectors from 'core/selectors/selectors';

export default class SepaPaymentMethod extends PaymentMethod {
    async beforePlaceOrder(): Promise<void> {
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountHolder'), 'Test Acceptatie');
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountNumber'), '******************');

        const bicInput = wooPlugin.page.locator(wooPlugin.getSelector('paymentMethods.sepa.bicNumberInput'));
        if (await bicInput.isVisible()) {
            await bicInput.fill('TESTNL2A');
        }
    }

    async onAdminVerifyOrderStatus(): Promise<void> {
        const orderData = await new OrderApiService().getOrderStatus(wooPlugin.sharedData['orderNumber']);

        expect(['on-hold', 'processing']).toContain(orderData.status);
        expect(orderData.amounts?.total).toEqual(wooPlugin.sharedData['orderSummary']['totals']['total']);

        await this.executeSepaPlazaScenario();
    }

    async onBuckarooTransactionVerify(): Promise<void> {
        if (!wooPlugin.sharedData['bckTransactionKey']) return;

        const plazaTransactionKey = wooPlugin.sharedData['bckTransactionKey'];
        await new AdminOrderService().verifyBuckarooTransaction(wooPlugin.sharedData['orderNumber']);
        wooPlugin.sharedData['bckTransactionKey'] = plazaTransactionKey;
    }

    private async executeSepaPlazaScenario(): Promise<void> {
        await new PlazaLoginPage().login();
        await new PlazaService().navigateToAdminPage('/Transaction/Transactions/Index');
        await this.retrySearchAndClickFirstRow(wooPlugin.sharedData['orderNumber']);
    }

    private async setupSearchCriteria(orderNumber: string): Promise<void> {
        const filterButton = wooPlugin.page.locator(selectors.plaza.search.filterButton);
        if (await filterButton.isVisible()) await filterButton.click();

        await wooPlugin.page.locator(selectors.plaza.search.searchField).fill(orderNumber);

        const transactionTypeInput = wooPlugin.page.locator(selectors.plaza.search.transactionTypeInput);
        await transactionTypeInput.click();
        await transactionTypeInput.fill('C004 SEPA Direct Debit');

        const sepaOption = wooPlugin.page.locator('li:has-text("C004 SEPA Direct Debit")').first();
        if (await sepaOption.isVisible()) {
            await sepaOption.click();
        } else {
            await transactionTypeInput.press('Enter');
        }

        // Set today's date - click on date field and select current day number
        await wooPlugin.page.locator(selectors.plaza.search.dateField).click();
        await wooPlugin.page.waitForTimeout(500); // Wait for date picker to open

        const today = new Date().getDate().toString();
        const todayCell = wooPlugin.page.locator(`${selectors.plaza.search.datePicker}:has-text("${today}")`).first();
        await todayCell.click();
    }

    private async clickSearchButton(): Promise<void> {
        const searchButton = wooPlugin.page.locator(selectors.plaza.search.searchButton);
        if (await searchButton.isVisible()) {
            await searchButton.click();
        } else {
            await wooPlugin.page.locator(selectors.plaza.search.searchField).press('Enter');
        }
    }

    private async retrySearchAndClickFirstRow(orderNumber: string): Promise<void> {
        await this.setupSearchCriteria(orderNumber);

        for (let attempt = 1; attempt <= 10; attempt++) {
            await this.clickSearchButton();
            await wooPlugin.page.waitForTimeout(5000);

            const rows = await wooPlugin.page.locator(selectors.plaza.search.transactionRows).count();
            if (rows > 0) {
                const matchingRow = await this.findTransactionRow(orderNumber);
                const targetRow = matchingRow || wooPlugin.page.locator(selectors.plaza.search.transactionRows).first();

                await targetRow.click();
                await this.extractTransactionKey();
                await this.executeScenarioOnCurrentPage();
                return;
            }
        }
        throw new Error('Failed to find transaction results after 10 attempts');
    }

    private async findTransactionRow(orderNumber: string): Promise<any> {
        const allRows = await wooPlugin.page.locator(selectors.plaza.search.transactionRows).all();
        for (const row of allRows) {
            const rowText = await row.textContent();
            if (rowText?.includes(orderNumber)) return row;
        }
        return null;
    }

    private async extractTransactionKey(): Promise<void> {
        const currentUrl = wooPlugin.page.url();
        const transactionKeyMatch = currentUrl.match(/transactionKey=([^&]+)/);

        if (transactionKeyMatch) {
            wooPlugin.sharedData['bckTransactionKey'] = transactionKeyMatch[1];
            console.log(`Extracted Transaction Key: ${transactionKeyMatch[1]}`);
        }
    }

    private async executeScenarioOnCurrentPage(): Promise<void> {
        await wooPlugin.page.locator(selectors.plaza.testScenario.actionsButton).click();
        await wooPlugin.page.locator(selectors.plaza.testScenario.testScenarioLink).click();

        const liLocator = wooPlugin.page.locator(selectors.plaza.testScenario.findScenario('Direct debit succeeds'));
        await liLocator.locator('> a').click();
        await liLocator.locator('div.k-content').waitFor({ state: 'visible' });
        await liLocator.locator(selectors.plaza.testScenario.performScenarioButton).click();

        const successMessage = wooPlugin.page.locator(selectors.plaza.testScenario.successMessage);
        await successMessage.waitFor({ state: 'visible', timeout: 30000 });

        const successText = await successMessage.textContent();
        if (!successText?.includes('Successfully performed scenario')) {
            throw new Error(`Unexpected success message: ${successText}`);
        }
    }
}
