import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';
import { OrderApiService } from '@woo/services/api';
import { expect } from '@woo/fixtures/BaseTest';
import AdminOrderService from '@woo/services/admin/AdminOrder.service';
import { PlazaService } from 'core/services/buckaroo';
import { PlazaLoginPage } from 'core/pages/plaza';
import TransactionStatusService from 'core/services/buckaroo/TransactionStatus.service';

export default class SepaPaymentMethod extends PaymentMethod {
    /**
     * Fills bank account required fields before placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        console.log('Filling SEPA bank account details...');

        // Fill bank account holder
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountHolder'), 'Test Acceptatie');

        // Fill IBAN (bank account number)
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.sepa.bankAccountNumber'), '******************');

        // Fill BIC number if visible (optional field)
        const bicInput = wooPlugin.page.locator(wooPlugin.getSelector('paymentMethods.sepa.bicNumberInput'));
        if (await bicInput.isVisible()) {
            await bicInput.fill('TESTNL2A');
        }

        // Press Tab to trigger any validation
        await wooPlugin.helper.resolveLocator('paymentMethods.sepa.bankAccountNumber').press('Tab');

        console.log('SEPA bank account details filled.');
    }

    /**
     * Handles SEPA on-hold status and executes Plaza scenario.
     */
    async onAdminVerifyOrderStatus(): Promise<void> {
        console.log('SEPA: Verifying order with on-hold status support...');

        const orderData = await new OrderApiService().getOrderStatus(wooPlugin.sharedData['orderNumber']);

        // Accept on-hold, processing, and pending as valid SEPA statuses
        const validStatuses = ['on-hold', 'processing', 'pending'];
        expect(validStatuses).toContain(orderData.status);
        expect(orderData.amounts?.total).toEqual(wooPlugin.sharedData['orderSummary']['totals']['total']);

        console.log(`SEPA order verified with status: ${orderData.status}`);

        // Execute Plaza scenario
        await this.executeSepaPlazaScenario();
    }

    /**
     * Verifies SEPA transaction using extracted transaction key.
     */
    async onBuckarooTransactionVerify(): Promise<void> {
        if (!wooPlugin.sharedData['bckTransactionKey']) {
            console.log('No transaction key available for SEPA.');
            return;
        }

        // Store our Plaza-extracted key to prevent overwriting
        const plazaTransactionKey = wooPlugin.sharedData['bckTransactionKey'];
        console.log(`Using Plaza-extracted transaction key: ${plazaTransactionKey}`);

        const adminOrderService = new AdminOrderService();
        await adminOrderService.verifyBuckarooTransaction(wooPlugin.sharedData['orderNumber']);

        // Restore our Plaza-extracted key (in case it was overwritten)
        wooPlugin.sharedData['bckTransactionKey'] = plazaTransactionKey;
    }

    /**
     * Executes Plaza scenario for SEPA orders.
     */
    private async executeSepaPlazaScenario(): Promise<void> {
        const plazaService = new PlazaService();
        const plazaLoginPage = new PlazaLoginPage();

        await plazaLoginPage.login();
        await plazaService.navigateToAdminPage('/Transaction/Transactions/Index');
        await this.retrySearchAndClickFirstRow(wooPlugin.sharedData['orderNumber']);
    }

    /**
     * Sets up the search criteria once (opens filter, sets search field and transaction type).
     */
    private async setupSearchCriteria(orderNumber: string): Promise<void> {
        // Open the filter panel
        const filterButton = wooPlugin.page.locator('#transaction_list > div > div.ibox-title > button');
        if (await filterButton.isVisible()) {
            await filterButton.click();
            await wooPlugin.page.waitForTimeout(1000);
        }

        // Set the search field to the order number
        const searchField = wooPlugin.page.locator('#search');
        if (await searchField.isVisible()) {
            await searchField.clear();
            await searchField.fill(orderNumber);
        }

        // Set the transaction type dropdown to "C004 SEPA Direct Debit"
        const transactionTypeInput = wooPlugin.page.locator('#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(3) > div > span > span.selection > span > ul > li > input');
        if (await transactionTypeInput.isVisible()) {
            await transactionTypeInput.click();
            await wooPlugin.page.waitForTimeout(500);
            await transactionTypeInput.fill('C004 SEPA Direct Debit');
            await wooPlugin.page.waitForTimeout(500);

            const sepaOption = wooPlugin.page.locator('li:has-text("C004 SEPA Direct Debit"), li:has-text("SEPA"), li:has-text("C004")').first();
            if (await sepaOption.isVisible()) {
                await sepaOption.click();
            } else {
                await transactionTypeInput.press('Enter');
            }
        }
    }

    /**
     * Clicks the search button to execute the search.
     */
    private async clickSearchButton(): Promise<void> {
        const searchButton = wooPlugin.page.locator('#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(12) > div > button');
        if (await searchButton.isVisible()) {
            await searchButton.click();
        } else {
            const searchField = wooPlugin.page.locator('#search');
            await searchField.press('Enter');
        }
    }



    /**
     * Retries search until we get results, then clicks on the first row.
     */
    private async retrySearchAndClickFirstRow(orderNumber: string): Promise<void> {
        await this.setupSearchCriteria(orderNumber);

        const maxRetries = 5;
        let attempt = 0;

        while (attempt < maxRetries) {
            attempt++;
            console.log(`Search attempt ${attempt}/${maxRetries}`);

            try {
                await this.clickSearchButton();
                await wooPlugin.page.waitForLoadState('networkidle');
                await wooPlugin.page.waitForTimeout(5000);

                const rows = await wooPlugin.page.locator('table tbody tr').count();

                if (rows > 0) {
                    const matchingRow = await this.findTransactionRow(orderNumber);
                    const targetRow = matchingRow || wooPlugin.page.locator('table tbody tr').first();

                    await targetRow.click();
                    await wooPlugin.page.waitForLoadState('networkidle');

                    // Extract transaction key from the Plaza transaction detail page
                    await this.extractTransactionKey();

                    await this.executeScenarioOnCurrentPage();
                    console.log('Plaza scenario executed successfully.');
                    return;
                }

                await wooPlugin.page.waitForTimeout(5000);

            } catch (error) {
                console.warn(`Search attempt ${attempt} failed:`, error);
                await wooPlugin.page.waitForTimeout(5000);
            }
        }

        throw new Error(`Failed to find transaction results after ${maxRetries} attempts`);
    }

    /**
     * Finds the transaction row for the given order number.
     */
    private async findTransactionRow(orderNumber: string): Promise<any> {
        const allRows = await wooPlugin.page.locator('table tbody tr').all();

        for (const row of allRows) {
            const rowText = await row.textContent();
            if (rowText?.includes(orderNumber)) {
                return row;
            }
        }

        return null;
    }



    /**
     * Extracts the transaction key from the Plaza transaction detail page.
     */
    private async extractTransactionKey(): Promise<void> {
        try {
            const currentUrl = wooPlugin.page.url();
            const transactionKeyMatch = currentUrl.match(/transactionKey=([^&]+)/);

            if (transactionKeyMatch) {
                const transactionKey = transactionKeyMatch[1];
                wooPlugin.sharedData['bckTransactionKey'] = transactionKey;
                console.log(`Extracted Transaction Key: ${transactionKey}`);
            }
        } catch (error) {
            console.warn('Error extracting transaction key:', error);
        }
    }

    /**
     * Executes the SEPA scenario on the current Plaza transaction page.
     */
    private async executeScenarioOnCurrentPage(): Promise<void> {
        const actionsButton = wooPlugin.page.locator('.btn.btn-default.btn-sm.dropdown-toggle');
        await actionsButton.click();

        const testScenarioLink = wooPlugin.page.locator('[title="Test scenarios"]');
        await testScenarioLink.click();

        const scenario = 'Direct debit succeeds';
        const liLocator = wooPlugin.page.locator(`#testCasePanelbar li:has(a:has-text("${scenario}"))`);
        const scenarioAnchor = liLocator.locator('> a');
        await scenarioAnchor.click();

        const accordionContent = liLocator.locator('div.k-content');
        await accordionContent.waitFor({ state: 'visible' });

        const performButton = accordionContent.locator('input[type="button"][value="Perform scenario"]');
        await performButton.click();

        const successMessage = wooPlugin.page.locator('#testCaseSuccessMessage');
        await successMessage.waitFor({ state: 'visible', timeout: 30000 });

        const successText = await successMessage.textContent();
        if (!successText?.includes('Successfully performed scenario')) {
            throw new Error(`Unexpected success message: ${successText}`);
        }
    }
}
