import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { PlazaTestScenarioService } from 'core/services/buckaroo';

export default class SepaPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after extracting transaction.
     */
    async afterAdminExtractBckTransaction(): Promise<void> {
        await new PlazaTestScenarioService().executeSteps({ scenario: 'Direct debit succeeds' });
        await this.processFlowService.pages.adminOrderDetailsPage.setOrderNumber(magentoPlugin.sharedData['orderNumber']).findOrderByID();
    }

    /**
     * Fills bank account required field in the payment method.
     */
    async beforePlaceOrder(): Promise<void> {
        console.log('Filling bank account...');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.sepa.bankAccountHolder'), 'Test Acceptatie');
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.sepa.bankAccountNumber'), '******************');

        const bicInput = magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.sepa.bicNumberInput'));
        (await bicInput.isVisible()) && (await bicInput.fill('TESTNL2A'));

        await magentoPlugin.helper.resolveLocator('paymentMethods.sepa.bankAccountNumber').press('Tab');

        console.log('Bank account filled.');
    }
}
