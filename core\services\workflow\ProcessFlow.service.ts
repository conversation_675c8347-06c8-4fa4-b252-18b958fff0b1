import { test } from '@playwright/test';
import { isFunction, upperFirst } from 'lodash-es';
import { ExecuteStepsParams, Hooks, Step } from '@core/services/workflow/ProcessFlow.types';
import { globalContext } from '@core/context/TestContext';
import { PaymentMethod } from '@core/services/payments';

export default abstract class ProcessFlowService<
    TStepName extends string = string,
    TPages extends Record<string, any> = Record<string, any>,
    TExecuteStepsParams extends ExecuteStepsParams<TStepName> = ExecuteStepsParams<TStepName>,
> {
    public readonly pages: TPages = {} as TPages;
    public options!: TExecuteStepsParams;

    /**
     * Retrieves the payment method gateway instance using the factory.
     *
     * @returns {PaymentMethod | undefined}
     */
    protected getPaymentMethodGateway(): PaymentMethod | undefined {
        return globalContext.paymentFactory.createPaymentMethod(this);
    }

    /**
     * Executes a sequence of steps with hooks.
     *
     * @param {ExecuteStepsParams} params
     */
    async executeSteps(params?: TExecuteStepsParams): Promise<void> {
        if (params) {
            this.options = params;
        }

        const paymentMethodGateway = this.getPaymentMethodGateway();

        for (const step of this.defineSteps()) {
            if (this.isStepDisabled(step, this.options?.disabledSteps ?? [])) {
                await this.onStepDisabled(step);
                continue;
            }

            const stepHooks = this.options?.hooks?.[step.name] ?? {};

            globalContext.testMode == 'positive'
                ? await this.executeStepHook('before', step, stepHooks, paymentMethodGateway)
                : await this.executeStepHook('beforeNegative', step, stepHooks, paymentMethodGateway);

            await this.executeStepAction(step, paymentMethodGateway);

            globalContext.testMode == 'positive'
                ? await this.executeStepHook('after', step, stepHooks, paymentMethodGateway)
                : await this.executeStepHook('afterNegative', step, stepHooks, paymentMethodGateway);
        }
    }

    /**
     * Checks if a step is disabled.
     *
     * @param step
     * @param {string[]} disabledSteps - Disabled step names.
     * @returns {boolean}
     */
    protected isStepDisabled(step: Step<TStepName>, disabledSteps: TStepName[]): boolean {
        return disabledSteps.includes(step.name) || (step.group ? disabledSteps.includes(step.group) : false);
    }

    /**
     * Executes hooks for a step.
     *
     * @param {string} hook
     * @param {Step} step
     * @param {Hooks} stepHooks
     * @param {PaymentMethod | undefined} paymentMethodGateway
     */
    protected async executeStepHook(
        hook: keyof Hooks<ProcessFlowService>,
        step: Step<TStepName>,
        stepHooks: Hooks<ProcessFlowService>,
        paymentMethodGateway?: PaymentMethod
    ): Promise<void> {
        await test.step(`${upperFirst(hook)} ${step.name}`, async () => {
            if (stepHooks[hook]) {
                await stepHooks[hook]!(this as ProcessFlowService);
            }

            const methodName = `${hook}${upperFirst(step.name)}`;
            if (paymentMethodGateway && isFunction(paymentMethodGateway[methodName as keyof PaymentMethod])) {
                await (paymentMethodGateway[methodName as keyof PaymentMethod] as () => Promise<void>)();
            }
        });
    }

    /**
     * Executes the action of a step.
     *
     * @param {Step} step - The step object.
     * @param {PaymentMethod | undefined} paymentMethodGateway
     */
    protected async executeStepAction(step: Step<TStepName>, paymentMethodGateway?: PaymentMethod): Promise<void> {
        await test.step(`Executing ${step.name}`, async () => {
            const methodName = `on${upperFirst(step.name)}`;
            if (paymentMethodGateway && isFunction(paymentMethodGateway[methodName as keyof PaymentMethod])) {
                await (paymentMethodGateway[methodName as keyof PaymentMethod] as () => Promise<void>)();
            } else {
                await step.action();
            }
        });
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected abstract defineSteps(): Step<TStepName>[];

    /**
     * Adds a step name to the disabledSteps array.
     *
     * @param stepName - The name of the step to disable.
     */
    public addDisabledStep(stepName: TStepName): void {
        if (!this.options.disabledSteps) {
            this.options.disabledSteps = [];
        }

        if (!this.options.disabledSteps.includes(stepName)) {
            this.options.disabledSteps.push(stepName);
        }
    }

    /**
     * Handles what happens when a step is disabled
     * This method can be overridden in child classes to implement custom behavior
     * @param step The step that is disabled
     */
    protected async onStepDisabled(step: Step<TStepName>): Promise<void> {
        console.log(`Step "${step.name}" is disabled. Skipping.`);
    }
}
