import { Page } from '@playwright/test';
import { AdminLoginPage, AdminOrderDetailsPage, AdminRefundPage } from '@magento2/pages/admin';
import { ExecuteStepsParams } from 'core/services/workflow';

export enum RefundSteps {
    StorefrontLogin = 'storefrontLogin',
    GoToOrder = 'goToOrder',
    GoToCreditMemo = 'goToCreditMemo',
    ProcessRefund = 'processRefund',
    VerifyRefund = 'verifyRefund',
    VerifyRefundTransaction = 'verifyRefundTransaction',
}

export type RefundPlacementPages = {
    page: Page;
    adminOrderDetailsPage: AdminOrderDetailsPage;
    adminLoginPage: AdminLoginPage;
    adminRefundPage: AdminRefundPage;
};

export interface RefundExecuteStepsParams extends ExecuteStepsParams<RefundSteps> {
    partial?: boolean;
}
