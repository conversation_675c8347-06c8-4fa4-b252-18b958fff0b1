export interface Currency {
    code: string; // ISO 4217 code, e.g., 'USD'
    symbols: string[]; // Currency symbols, e.g., ['$', 'US$']
    exponent: number; // Number of decimal places, e.g., 2 for USD
}

export interface Format {
    currencySymbol: string;
    currencyCode: string;
    symbolPosition: 'prefix' | 'suffix';
    decimalSeparator: string;
    thousandSeparator: string;
    negativeSign: string;
}

export interface ParseResult {
    value: number;
    floatValue: number;
    symbol: string;
    currencyCode: string;
    currency: Currency;
    format: Format; // Include format in the ParseResult
}

export interface ParseOptions {
    parseNegative?: boolean;
}
