import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('Multibanco Tests', () => {
    test('Place order with Multibanco', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
        });
    });

    test('Place order with Multibanco (as guest) & Refund from Plaza', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });

        await plazaRefundService.executeSteps();
    });

    test('Refund order with Multibanco (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Multibanco (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Place order with Multibanco (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_multibanco', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: '15' },
        });
    });

    test('Place order with Multibanco (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_multibanco', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.MULTIBANCO,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
