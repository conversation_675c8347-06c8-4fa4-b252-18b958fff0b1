import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('American Express Tests', () => {
    test('Place order with American Express', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'amex' },
        });
    });

    test('Place order with American Express (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
        });
    });

    test('Refund order with American Express (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with American Express (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'amex' },
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with American Express (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'amex' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with American Express (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.CREDITCARD_DEBITCARD, [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.CREDITCARD_DEBITCARD),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15, card: 'amex' },
        });
    });

    test('Place order with American Express (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.CREDITCARD_DEBITCARD, [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.CREDITCARD_DEBITCARD),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'amex' },
        });
    });

    test('Place order with American Express (NO)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'amex', responseStatus: 'N' },
        });
    });

    test('Place order with American Express (Unknown)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'amex', responseStatus: 'U' },
        });
    });
});
