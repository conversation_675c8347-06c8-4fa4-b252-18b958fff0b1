import { wooApiService } from '@woo/services/api/index';
import wooPlugin from '@woo/WooPlugin';

/**
 * Service class handling all checkout-related API operations
 */
export default class CheckoutApiService {
    /**
     * Fetches checkout data from the API and stores it in shared data.
     * This should be called after any changes to payment or shipping methods.
     * @returns {Promise<any>} The checkout data
     * @throws Will throw an error if the checkout data fetch fails.
     */
    async getCheckoutData(): Promise<any> {
        console.log('Fetching checkout data...');

        const checkoutData = await wooApiService().post('/actions/action/checkout_data');

        if (checkoutData.success) {
            wooPlugin.sharedData['orderSummary'] = checkoutData.data;
            console.log('Checkout data fetched successfully.');
            return checkoutData.data;
        }

        throw new Error('Failed to fetch checkout data. Message: ' + checkoutData.message);
    }

    // clear cart endpoint
    /**
     * Clears the cart by calling the WooCommerce API.
     * @returns {Promise<void>} A promise that resolves when the cart is cleared.
     */
    async clearCart(): Promise<void> {
        console.log('Clearing cart...');

        const response = await wooApiService().post('/actions/action/wc_empty_cart');

        if (response.success) {
            console.log('Cart cleared successfully.');
            return;
        }

        throw new Error('Failed to clear cart. Message: ' + response.message);
    }
}
