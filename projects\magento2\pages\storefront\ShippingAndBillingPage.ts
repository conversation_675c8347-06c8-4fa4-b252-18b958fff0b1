import magentoPlugin from '@magento2/MagentoPlugin';
import { StorefrontBasePage } from '@magento2/pages/storefront/index';
import { customerData } from '@core/selectors/customerData';
import { OrderExecuteStepsParams } from '@magento2/services/storefront';

export default class ShippingAndBillingPage extends StorefrontBasePage {
    /**
     * Proceeds to the checkout page.
     */
    async proceedToCheckout(): Promise<void> {
        await magentoPlugin.helper.clickAndExpectVisible('storefront.product.proceedToCheckout', 'Clicking the "Proceed to Checkout" button.');
    }

    async fillFormForGuest() {
        if (await this.isAlreadyLoggedIn()) {
            console.log('User is already logged in');
            return;
        }

        const customerFieldSelectors = magentoPlugin.getSelector('storefront.checkout.customerForm') as Record<string, string>;
        await magentoPlugin.page.fill(customerFieldSelectors['email'], customerData.email);
        await magentoPlugin.page.fill(customerFieldSelectors['firstName'], customerData.firstName);
        await magentoPlugin.page.fill(customerFieldSelectors['lastName'], customerData.lastName);
        await magentoPlugin.page.fill(customerFieldSelectors['company'], customerData.company);
        await magentoPlugin.page.fill(customerFieldSelectors['street1'], customerData.street1);
        await magentoPlugin.page.fill(customerFieldSelectors['street2'], customerData.street2);
        await magentoPlugin.page.selectOption(customerFieldSelectors['country'], customerData.country);
        await magentoPlugin.page.fill(customerFieldSelectors['state'], customerData.state);
        await magentoPlugin.page.fill(customerFieldSelectors['city'], customerData.city);
        await magentoPlugin.page.fill(customerFieldSelectors['zip'], customerData.zip);
        await magentoPlugin.page.fill(customerFieldSelectors['phone'], customerData.phone);
    }

    /**
     * Selects a shipping method based on the provided method name.
     * Ensures the provided method name is valid and resolves the corresponding selector.
     * @param methodKey - The ID of the shipping method to select (e.g., 'free', 'flat').
     */
    async selectShippingMethod(methodKey: OrderExecuteStepsParams<any>['shippingMethod'] = 'free'): Promise<void> {
        if (!methodKey) {
            throw new Error('Shipping method name must be provided.');
        }

        await magentoPlugin.helper.clickAndExpectVisible(
            `storefront.checkout.${methodKey}ShippingMethod` as any,
            `Selecting '${methodKey}' as the shipping method.`
        );
    }
}
