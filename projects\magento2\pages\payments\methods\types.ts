import { PaymentGatewayOptions } from 'core/services/workflow';

export interface CardInfo {
    name: string;
    number: string;
    expMonth: string;
    expYear: string;
    cvv?: string;
}

export interface GiftCardInfo extends PaymentGatewayOptions {
    number: string;
}

export interface CreditCardOptions extends PaymentGatewayOptions {
    card: string;
    cardDetails: {
        number: string;
        pin: string;
    };
}

export interface GiftCardOptions extends CreditCardOptions {
    amount?: number;
}

export interface IdealOptions extends PaymentGatewayOptions {
    showIssuers: boolean;
    responseStatus: '190' | '791' | '490' | '690' | '890';
}

export interface BelfiusOptions extends PaymentGatewayOptions {
    responseStatus: '190' | '490' | '890';
}

export interface In3Options extends PaymentGatewayOptions {
    responseStatus: '190' | '490' | '690' | '791' | '890';
}

export interface KbcOptions extends PaymentGatewayOptions {
    responseStatus: '190' | '490' | '690' | '890';
}

export interface CreditCardOptions extends PaymentGatewayOptions {
    responseStatus: 'Y' | 'A' | 'N' | 'U';
}

export interface Przelewy24Options extends PaymentGatewayOptions {
    responseStatus: '190' | '490' | '690' | '890';
}
