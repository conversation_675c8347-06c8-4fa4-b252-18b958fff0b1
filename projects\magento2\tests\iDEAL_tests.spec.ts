import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { AdminBuckarooConfigPage, AdminConfigPage } from '@magento2/pages/admin';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import AdminOrderCreateInvoice from '@magento2/pages/admin/AdminOrderCreateInvoice';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { env } from '@/utils/env';

test.describe('iDEAL Tests', () => {
    test('Place order with iDEAL', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
        });
    });

    test('Place order with iDeal (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Place order with iDeal (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 2,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });
    });

    test('Refund order with iDeal (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with iDeal (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with iDeal (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with iDEAL (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('ideal', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.IDEAL),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with iDEAL (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('ideal', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.IDEAL),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with iDEAL (Failed)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            // shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '490' },
        });
    });

    test('Place order with iDEAL (Rejected)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            paymentGatewayOptions: { responseStatus: '690' },
        });
    });

    test('Place order with iDEAL (Canceled)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });

    test('Place order with iDEAL (create invoice on shipment)', async ({ orderService }) => {
        await new AdminConfigPage().updateInvoiceHandling('shipment');

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
            shippingMethod: 'flat',
        });

        await new AdminOrderCreateInvoice().setOrderNumber(magentoPlugin.sharedData['orderNumber']).createShipmentInvoice();

        await new AdminConfigPage().updateInvoiceHandling('payment');
    });

    test('Place order and Refund with iDEAL with content type "soap"', async ({ orderService, refundService }) => {
        test.skip(env('STAGE') !== 'refactor', 'Skipping test because STAGE is not "refactor".');

        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.soap);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with iDEAL with content type "json"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with iDEAL with content type "httppost"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.IDEAL,
        });

        await refundService.executeSteps();
    });
});
