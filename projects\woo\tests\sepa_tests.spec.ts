import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('SEPA Tests', () => {
    test('Place order with SEPA', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'flat',
        });
    });

    test('Place order with SEPA (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Place order with SEPA (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with SEPA (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'free',
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Partial refund order with SEPA (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with SEPA (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with SEPA (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_sepadirectdebit', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: '15' },
        });
    });

    test('Place order with SEPA (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_sepadirectdebit', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order and Refund with SEPA with content type "json"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with SEPA with content type "httppost"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.SEPA,
        });

        await refundService.executeSteps();
    });
});
