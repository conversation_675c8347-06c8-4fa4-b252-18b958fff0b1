import { expect, Locator } from '@playwright/test';
import magentoPlugin from '@magento2/MagentoPlugin';
import { StorefrontBasePage } from '@magento2/pages/storefront/index';
import { isNil, isString } from 'lodash-es';
import { OrderSummary } from '@magento2/services/storefront';
import OrderSummaryService from '@core/services/workflow/OrderSummary.service';

export default class CheckoutPage extends StorefrontBasePage {
    /**
     * Extracts the order summary details from the checkout page.
     * @returns An object containing all extracted order summary values.
     */
    async getOrderSummary(): Promise<OrderSummary> {
        await magentoPlugin.helper.waitToBeVisible('storefront.checkout.orderSummary.blockSummary', { timeout: 30000 });

        return await new OrderSummaryService<OrderSummary>(magentoPlugin.getSelector('storefront.checkout.orderSummary.prices')).parse();
    }

    /**
     * Selects a payment method by its identifier.
     * @param paymentMethod - The identifier for the payment method.
     */
    async selectPaymentMethod(paymentMethod: string): Promise<void> {
        const paymentMethodLocator: Locator = magentoPlugin.page.locator(
            magentoPlugin.getSelector('storefront.checkout.selectPaymentMethod')(paymentMethod)
        );

        const paymentMethodTitle: Locator = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.checkout.paymentMethodTitle'));

        await expect(paymentMethodTitle, 'The "Payment Method" header is not visible on checkout, Payment methods may not have loaded').toBeVisible({
            timeout: 10000,
        });
        await expect(
            paymentMethodLocator,
            `The Payment Method '${paymentMethod}' is not visible on checkout (it might be disabled on the payment configuration settings)`
        ).toBeVisible({ timeout: 10000 });

        await paymentMethodLocator.click();
        console.log(`Payment method '${paymentMethod}' selected.`);

        magentoPlugin.sharedData['orderSummary'] = await this.getOrderSummary();
    }

    /**
     * Places the order.
     */
    async placeOrder(): Promise<void> {
        await magentoPlugin.helper.clickIfVisible(
            magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.checkout.placeOrderButton')),
            'Place Order button not found.'
        );
    }

    /**
     * Fills the date of birth field.
     * @param date - The date to enter in the field.
     */
    async fillDateOfBirth(date: string): Promise<void> {
        const dateOfBirth = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.checkout.dateOfBirth'));
        await dateOfBirth.fill(date);
        console.log(`Entered date of birth: ${date}`);
        await dateOfBirth.press('Tab');
    }

    /**
     * Navigates to the review and payments section.
     */
    async reviewAndPayments(): Promise<void> {
        await magentoPlugin.helper.waitForVisibleAndClick('storefront.checkout.reviewAndPayments', 'Navigating to review and payments.');
    }

    /**
     * Clicks the "Pay Now" button.
     */
    async payNow(): Promise<void> {
        await magentoPlugin.helper.waitForVisibleAndClick('paymentMethods.creditCards.payNowBtn', 'Clicking "Pay Now" button.');
    }

    /**
     * Clicks the "Submit Status" button.
     * @param status - The status code to submit. Defaults to '190'.
     */
    async submitStatus(status: any = '190'): Promise<void> {
        await magentoPlugin.page.selectOption(magentoPlugin.getSelector('storefront.checkout.selectStatus'), status);
        await magentoPlugin.helper.waitForVisibleAndClick('storefront.checkout.submitStatus', 'Submitting status.');
    }

    /**
     * Continues to the order review step.
     */
    async continueToReviewOrder(): Promise<void> {
        await magentoPlugin.helper.waitForVisibleAndClick('paymentMethods.payPal.continueToReviewOrder', 'Continuing to review order.');
    }

    /**
     * Completes wallet steps by interacting with sequential step buttons.
     */
    async completeWalletSteps(): Promise<void> {
        const steps = [
            { label: 'Login', selector: magentoPlugin.getSelector('paymentMethods.walletMethods.loginButton') },
            {
                label: 'Make Payment',
                selector: magentoPlugin.getSelector('paymentMethods.walletMethods.makePaymentButton'),
            },
            {
                label: 'Back to where you came from',
                selector: magentoPlugin.getSelector('paymentMethods.walletMethods.backButton'),
            },
        ];

        for (const step of steps) {
            await magentoPlugin.helper.waitToBeVisible(step.selector, { timeout: 10000 });
            await magentoPlugin.helper.waitForVisibleAndClick('storefront.checkout.nextStepWallet', `Completing wallet step: ${step.label}`);
        }
    }

    /**
     * Verifies that the payment fee in the order summary matches the expected fee.
     * @param paymentFee - The expected payment fee to verify against the order summary.
     * @throws Will throw an error if the payment fee does not match the expected value.
     */
    async checkPaymentFee(paymentFee: number | string): Promise<void> {
        const orderSummary = await this.getOrderSummary();

        if (isNil(orderSummary.buckarooFee)) {
            throw new Error('Buckaroo fee is not defined in the order summary.');
        }

        let calculatedFee: number | string;

        if (isString(paymentFee) && paymentFee.endsWith('%')) {
            const percentage = parseFloat(paymentFee);
            if (isNaN(percentage)) {
                throw new Error('Invalid percentage format.');
            }

            if (isNil(orderSummary.subtotal)) {
                throw new Error('Total amount is not defined in the order summary for percentage calculation.');
            }

            calculatedFee = (((orderSummary.subtotal + (orderSummary.totalTax ?? 0)) * percentage) / 100).toFixed(2);
        } else {
            calculatedFee = paymentFee;
        }

        if (orderSummary.buckarooFee != calculatedFee) {
            throw new Error(`Expected fee to be ${calculatedFee} but found ${orderSummary.buckarooFee}.`);
        }

        console.log(`Payment fee of ${paymentFee} (${calculatedFee}) is correct.`);
    }

    async verifyPaymentError(expectedMessage: string): Promise<void> {
        const errorSelector = magentoPlugin.getSelector('storefront.checkout.paymentErrorMessage');

        await magentoPlugin.helper.expectToBeVisible(errorSelector, { timeout: 10000 });

        expect((await magentoPlugin.page.locator(errorSelector).textContent())?.toLowerCase()).toContain(expectedMessage);
        console.log('Order failed as expected');
    }
}
