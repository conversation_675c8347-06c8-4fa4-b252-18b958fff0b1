import { Step } from '@core/services/workflow/ProcessFlow.types';
import { PlazaLoginPage, PlazaOrderDetailsPage, PlazaRefundPage } from 'core/pages/plaza';
import { ProcessFlowService } from '@core/services/workflow';
import { PlazaRefundPlacementPages, PlazaRefundSteps } from '@core/services/buckaroo/Plaza.types';
import { globalContext } from '@core/context/TestContext';

export default class PlazaRefundService<
    IPlazaRefundSteps extends string = PlazaRefundSteps,
    IPlazaRefundPlacementPages extends PlazaRefundPlacementPages = PlazaRefundPlacementPages,
> extends ProcessFlowService<IPlazaRefundSteps, IPlazaRefundPlacementPages> {
    constructor() {
        super();

        this.pages.plazaLoginPage = new PlazaLoginPage();
        this.pages.plazaOrderDetailsPage = new PlazaOrderDetailsPage();
        this.pages.plazaRefundPage = new PlazaRefundPage();
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<IPlazaRefundSteps>[] {
        return [
            {
                name: PlazaRefundSteps.PlazaLogin as IPlazaRefundSteps,
                action: async () => {
                    await this.pages.plazaLoginPage.login();
                },
            },
            {
                name: PlazaRefundSteps.GoToTransactionPage as IPlazaRefundSteps,
                action: async () => {
                    await this.pages.plazaOrderDetailsPage.navigateToTransaction(globalContext.sharedData['bckTransactionKey']);
                },
            },
            {
                name: PlazaRefundSteps.ProcessRefund as IPlazaRefundSteps,
                action: async () => {
                    await this.pages.plazaRefundPage.processRefund();
                },
            },
            {
                name: PlazaRefundSteps.VerifyRefund as IPlazaRefundSteps,
                action: async () => {
                    await this.pages.plazaRefundPage.verifyRefundSuccess();
                },
            },
        ];
    }
}
