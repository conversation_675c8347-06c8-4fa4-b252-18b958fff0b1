import BasePage from '@core/pages/BasePage';
import wooPlugin from '@woo/WooPlugin';
import { expect } from '@woo/fixtures/BaseTest';
import { env } from '@/utils/env';
import { chain, round } from 'lodash-es';
import { globalContext } from '@core/context/TestContext';
import { Dialog } from '@playwright/test';
import { OrderApiService } from '@woo/services/api';
import { TransactionStatusService } from '@core/services/buckaroo';

export default class AdminOrderDetailsPage extends BasePage {
    public orderNumber!: string;

    /**
     * Navigates to the checkout page.
     */
    async navigateHere(): Promise<void> {
        await wooPlugin.page.goto(`${env('BASE_URL')}/wp-admin/admin.php?page=wc-orders&action=edit&id=${this.orderNumber}`);
    }

    public setOrderNumber(orderNumber: string) {
        this.orderNumber = orderNumber;
        return this;
    }

    /**
     * Processes the refund by clicking the refund button and verifying that the order is closed.
     */
    public async processRefund(isPartial: boolean = true): Promise<void> {
        const refundBtn = wooPlugin.page.locator('.add-items button.refund-items');

        await Promise.all([
            refundBtn.click(),
            wooPlugin.page.locator('input[name="refund_amount"]').first().waitFor({ state: 'visible', timeout: 2000 }),
        ]);

        const totalText = wooPlugin.sharedData['orderSummary']['totals']['total'];

        console.log(`Total amount: ${totalText}`);
        const refundAmountText = Number(isPartial ? round(totalText / 2, 2) : totalText)
            .toFixed(2)
            .replace('.', ',');

        console.log(`Total to refund: ${refundAmountText}`);

        // TMP
        await wooPlugin.page.evaluate(() => {
            document.querySelectorAll('#refund_amount')[0].removeAttribute('readonly');
        });

        await wooPlugin.page.fill('input[name="refund_amount"]', refundAmountText);

        globalContext.page.once('dialog', async (dialog: Dialog) => {
            console.log(`Dialog message: ${dialog.message()}`);
            await dialog.accept();
        });

        await wooPlugin.page.click('button.do-api-refund');

        await wooPlugin.page.locator('.woocommerce-order-items .blockUI.blockOverlay').waitFor({ state: 'detached' });
        await wooPlugin.page.waitForURL(/.*/, { waitUntil: 'networkidle' });

        await this.verifyOrderStatus(isPartial ? 'processing' : 'refunded');
    }

    /**
     * Processes the refund by clicking the refund button and verifying that the order is closed.
     */
    public async verifyRefund(): Promise<void> {
        const orderDetails = await new OrderApiService().getOrderStatus(this.orderNumber);

        const [fullKey, refundValue] =
            chain(orderDetails.post_meta)
                .toPairs()
                .find(([key]) => key.startsWith('_refundbuckaroo'))
                .value() || [];

        const bckTransactionKey = fullKey?.replace('_refundbuckaroo', '');

        expect(refundValue).toBe('ok');

        console.log(`Refund Transaction ID: ${bckTransactionKey}`);

        const transactionService = new TransactionStatusService(bckTransactionKey);
        const transactionResponse = await transactionService.status();

        expect(transactionResponse.isSuccess()).toBe(true);
        console.log('The Transaction is Successful.');
    }

    /**
     * Verifies that the refund is reflected on the customer side (Plaza),
     * ensuring the order status is updated and the refunded amount matches the expected value.
     */
    public async verifyPlazaRefundSuccess(expectedOrderStatus: string = 'processing'): Promise<void> {
        await wooPlugin.helper.executeWithRetry(
            async () => {
                await this.verifyOrderStatus(expectedOrderStatus);
                await this.verifyRefund();
            },
            {
                retries: 10,
                delay: 5000,
                errorMessage: 'Failed to verify the refunded amount after 5 attempts.',
                logMessage: 'Verifying the refunded amount',
            }
        );
    }

    /**
     * Verifies that the order status matches the expected status.
     * @param expectedStatus - The expected order status (e.g., 'Closed').
     */
    public async verifyOrderStatus(expectedStatus: string = 'Processing'): Promise<void> {
        console.log(`Verifying order status to be "${expectedStatus}"`);

        const refundedAmount = wooPlugin.page.locator('.total.refunded-total .amount bdi');
        const status = (await wooPlugin.page.locator('#order_status').inputValue()).replace('wc-', '');

        expect(await refundedAmount.isVisible()).toBe(true);
        expect(status).toBe(expectedStatus);
    }
}
