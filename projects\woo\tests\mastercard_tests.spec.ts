import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('Mastercard Tests', () => {
    test('Place order with Mastercard', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'mastercard' },
        });
    });

    test('Place order with Mastercard (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'mastercard' },
        });
    });

    test('Refund order with Mastercard (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'mastercard' },
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Mastercard (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { card: 'mastercard' },
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Mastercard (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'mastercard' },
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Mastercard (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcard', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15, card: 'mastercard' },
        });
    });

    test('Place order with Mastercard (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_creditcards', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%', card: 'mastercard' },
        });
    });

    test('Place order with Mastercard (NO)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            paymentGatewayOptions: { card: 'mastercard', responseStatus: 'N' },
        });
    });

    test('Place order with Mastercard (Unknown)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.CREDITCARD_DEBITCARD,
            shippingMethod: 'flat',
            paymentGatewayOptions: { card: 'mastercard', responseStatus: 'U' },
        });
    });
});
