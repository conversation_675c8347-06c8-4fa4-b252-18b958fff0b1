import BasePage from '@core/pages/BasePage';
import { Dialog } from '@playwright/test';
import { PlazaService } from '@core/services/buckaroo';
import { expect } from '@core/fixtures/BaseTest';
import { PlazaOrderDetailsPage } from '@core/pages/plaza/index';
import moment from 'moment';
import { globalContext } from '@core/context/TestContext';

export default class PlazaRefundPage extends BasePage {
    public refundAmount: string = '10';
    protected plazaService: PlazaService;
    protected plazaOrderDetailsPage!: PlazaOrderDetailsPage;

    constructor() {
        super();
        this.plazaService = new PlazaService();
        this.plazaOrderDetailsPage = new PlazaOrderDetailsPage();
    }

    /**
     * Processes a refund by performing necessary admin actions.
     */
    async processRefund(): Promise<void> {
        const orderNumber = await this.plazaOrderDetailsPage.getOrderNumber();
        if (!orderNumber) {
            throw new Error('Order number could not be retrieved.');
        }

        globalContext.sharedData['orderNumber'] = orderNumber;
        console.log(`Order Number: ${orderNumber}`);

        await globalContext.page.locator(this.plazaRefundSelectors.actionsButton).click();
        await globalContext.page.locator(this.plazaRefundSelectors.refundLink).click();
        await globalContext.page.locator(this.plazaRefundSelectors.refundAmountInput).fill(this.refundAmount);
        await this.handleRefundDialog();
    }

    /**
     * Handles the refund confirmation dialog.
     */
    private async handleRefundDialog(): Promise<void> {
        globalContext.page.once('dialog', async (dialog: Dialog) => {
            console.log(`Dialog message: ${dialog.message()}`);
            await dialog.accept();
        });

        await globalContext.page.locator(this.plazaRefundSelectors.executeRefundButton).click();
    }

    get plazaRefundSelectors() {
        return globalContext.getSelector('plaza.refund');
    }

    /**
     * Verifies the refund success on the user side.
     */
    public async verifyRefundSuccess(): Promise<void> {
        const expectedSuccessMessage = 'The refund is successfully processed.';

        await globalContext.page.locator(this.plazaRefundSelectors.successMessage).waitFor();

        const successMessage = await globalContext.helper.getText(this.plazaRefundSelectors.successMessage, {
            errorMessage: 'Transaction Successful message does not exists',
        });
        expect(successMessage, `The message '${expectedSuccessMessage}' was not displayed after processing the refund on Plaza.`).toBe(
            expectedSuccessMessage
        );

        await globalContext.helper.expectToBeVisible('plaza.refund.successStatusCell');

        const transactionDateText = await globalContext.page.locator(this.plazaRefundSelectors.dateAndCreadedByDetails).innerText();
        const dateTimeOnly = transactionDateText.split(' ').slice(0, 2).join(' ');

        globalContext.sharedData['plazaRefundDate'] = moment(dateTimeOnly, ['M/D/YYYY h:mm:ss A', 'DD/MM/YYYY HH:mm:ss A', 'MM/DD/YYYY hh:mm:ss A']);
    }
}
