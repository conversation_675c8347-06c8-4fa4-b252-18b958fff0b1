import { PaymentMethodCode, PaymentMethods } from '@/utils/paymentMethods';
import ProcessFlowService from '@core/services/workflow/ProcessFlow.service';
import { PaymentMethod, PaymentMethodConstructor } from '@core/services/payments';
import * as PaymentMethodsMap from '@core/pages/payments/methods';

export default class PaymentFactory {
    /**
     * Registry mapping payment method keys to their respective classes.
     */
    public static readonly paymentMethodRegistry: Partial<Record<PaymentMethodCode, PaymentMethodConstructor<any>>> = {
        [PaymentMethods.IDEAL]: PaymentMethodsMap.IdealPaymentMethod,
    };

    /**
     * Creates a payment method instance based on the provided parameters.
     * @returns The instantiated payment method, or undefined if not found.
     * @param processFlowService
     */
    static createPaymentMethod(processFlowService: ProcessFlowService): PaymentMethod | undefined {
        const methodKey = processFlowService.options?.paymentMethod;

        if (!methodKey) {
            return undefined;
        }

        const PaymentClass = this.paymentMethodRegistry[methodKey];

        if (!PaymentClass) {
            console.warn(`Payment method not found for key: ${methodKey}`);
            return undefined;
        }

        return new PaymentClass(processFlowService);
    }
}
