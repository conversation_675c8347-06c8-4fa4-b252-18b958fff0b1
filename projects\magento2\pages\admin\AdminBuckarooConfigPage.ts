import { PaymentMethods } from '@/utils/paymentMethods';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminPage } from '@magento2/pages/admin/index';
import { ConfigValue } from '@magento2/pages/admin/types';

export default class AdminBuckarooConfigPage extends AdminPage {
    /**
     * Set Buckaroo payment method configuration.
     * @param paymentMethod - Payment method identifier.
     * @param configValues - Key-value pairs to configure.
     */
    public async setConfig(paymentMethod: string, configValues: ConfigValue[]): Promise<void> {
        console.log(`Setting config for payment method: ${paymentMethod}`);
        await this.loginToAdmin();
        await this.navigateToPaymentMethodsConfig();
        await this.openPaymentMethodConfigSection(paymentMethod);
        await this.setConfigValues(configValues);
        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
    }

    /**
     * Set configuration for all payment methods.
     * @param configValues - Key-value pairs to apply to all payment methods.
     */
    public async setConfigsForAllPaymentMethods(configValues: ConfigValue[]): Promise<void> {
        const paymentMethodsWithoutFee: string[] = [
            PaymentMethods.PAYBYBANK,
            PaymentMethods.GIFTCARDS,
            PaymentMethods.BOEKENBON,
            PaymentMethods.FASHION_CHEQUE,
            PaymentMethods.VVVGIFTCARD,
        ];

        console.log('Setting config for all payment methods');
        await this.loginToAdmin();
        await this.navigateToPaymentMethodsConfig();

        for (const methodKey of Object.keys(PaymentMethods)) {
            const paymentMethod = PaymentMethods[methodKey as keyof typeof PaymentMethods];
            console.log(`Configuring payment method: ${paymentMethod}`);

            if (!paymentMethodsWithoutFee.includes(paymentMethod)) {
                await this.openPaymentMethodConfigSection(paymentMethod);
                await this.setConfigValues(configValues);
            }

            // Hide all extended payment method sections
            const extendedSections = await magentoPlugin.page
                .locator(magentoPlugin.getSelector('admin.config.extendedPaymentMethodSection')(paymentMethod))
                .all();
            for (const extendedSection of extendedSections.reverse()) {
                await extendedSection.click();
            }

            await magentoPlugin.helper.clickIfVisible(magentoPlugin.page.locator(magentoPlugin.getSelector('admin.config.closeBtn')));
        }

        await this.saveConfig();
        await this.verifySaveSuccessMessage('You saved the configuration.');
    }
}
