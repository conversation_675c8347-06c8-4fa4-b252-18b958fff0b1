import { KlarnaPaymentMethod } from '@magento2/pages/payments';

export default class KlarnaPayPaymentMethod extends KlarnaPaymentMethod {
    /**
     * Handles actions before placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        await super.selectCustomerGender('male');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await super.afterPlaceOrder();
    }
}
