# QA Platform Tests

This repository contains automated tests for e-commerce platforms using Playwright.

## Getting Started

### Prerequisites

- Node.js (v20.x or later)
- npm (v10.x or later)
- Git

### Installation

1. Clone the repository:

    ```bash
    <NAME_EMAIL>:buckaroo-it/QAPlatform_tests.git
    cd QAPlatform_tests
    ```

2. Install dependencies:

    ```bash
    npm ci
    ```

3. Install Playwright browsers:
    ```bash
    npx playwright install
    ```

## Environment Setup

The project uses different environment files for different e-commerce platforms:

1. Copy the example environment file:

    ```bash
    cp .env.magento2.example .env.magento2
    ```

2. Edit the `.env.magento2` file with your specific configuration (URLs, credentials, etc.)

## Running Tests

### Magento 2 Tests

```bash
npm run test:magento2
```

To run a specific test file:

```bash
npx playwright test --config=projects/magento2/playwright.config.ts path/to/test/file.spec.ts
```

To run tests in UI mode:

```bash
npx playwright test --config=projects/magento2/playwright.config.ts --ui
```

## Project Structure

```
QAPlatform_tests/
├── core/               # Core functionality shared across projects
├── projects/           # Project-specific tests and configurations
│   ├── magento2/       # Magento 2 specific tests
│   │   ├── fixtures/   # Test fixtures
│   │   ├── pages/      # Page objects
│   │   ├── selectors/  # UI selectors
│   │   ├── services/   # Testing services
│   │   └── tests/      # Test specifications
└── utils/              # Utility functions
```

## Code Quality

To check TypeScript types:

```bash
npm run type-check
```

To format code using Prettier:

```bash
npx prettier --write "**/*.{ts,js,json,yml,yaml,md}"
```

## Troubleshooting

- If tests fail due to timeout issues, adjust the timeout in the `playwright.config.ts` file.
- For debugging, you can set `headless: false` in the configuration file to see the browser UI during test execution.

## Additional Resources

- [Playwright Documentation](https://playwright.dev/docs/intro)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
