import magentoPlugin from '@magento2/MagentoPlugin';
import { isNil } from 'lodash-es';
import { StorefrontBasePage } from '@magento2/pages/storefront/index';

export default class OrderSuccessfulPage extends StorefrontBasePage {
    /**
     * Retrieves the order number from the details page.
     */
    public async getOrderNumber(): Promise<string> {
        const orderNumber = await this.extractOrderNumber();
        magentoPlugin.sharedData['orderNumber'] = orderNumber;
        return orderNumber;
    }

    /**
     * Extracts the order number from the current page.
     * @returns The extracted order number.
     */
    public async extractOrderNumber(): Promise<string> {
        await magentoPlugin.page.waitForLoadState('networkidle');

        if (await this.isAlreadyLoggedIn()) {
            return (await magentoPlugin.helper.getText(magentoPlugin.getSelector('storefront.successfulPage.orderNumber'), {
                strict: true,
                errorMessage: 'Order number element not found.',
            })) as string;
        }

        await magentoPlugin.page.waitForSelector(magentoPlugin.getSelector('storefront.successfulPage.orderConfirmationDetails'));

        const orderNumberElement = magentoPlugin.page.locator(magentoPlugin.getSelector('storefront.successfulPage.orderNumberAsGuest'));

        if (isNil(orderNumberElement)) {
            throw new Error('Order number element not found.');
        }

        return String(await magentoPlugin.helper.getText(orderNumberElement));
    }

    /**
     * Waits for the success page URL to be loaded.
     */
    public async waitForSuccessPage(): Promise<void> {
        await magentoPlugin.page.waitForURL(/.*\/onepage\/success\/.*/, { timeout: 30000 });
        magentoPlugin.sharedData['orderNumber'] = await this.getOrderNumber();
    }
}
