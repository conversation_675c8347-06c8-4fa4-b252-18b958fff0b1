import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { OrderSteps } from '@woo/services/storefront';
import { BuckarooSettingsApiService } from '@woo/services/api';

test.describe('EPS Tests', () => {
    test('Place order with EPS', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'flat',
        });
    });

    test('Place order with EPS (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Place order with EPS (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with EPS (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'free',
        });

        await refundService.executeSteps({ partial: false });
    });

    test('Partial refund order with EPS (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 20,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with EPS (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with EPS (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_eps', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: '15' },
        });
    });

    test('Place order with EPS (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_eps', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order and Refund with EPS with content type "json"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
        });

        await refundService.executeSteps();
    });

    test('Place order and Refund with EPS with content type "httppost"', async ({ orderService, refundService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.EPS,
        });

        await refundService.executeSteps();
    });
});
