import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('Klarna Slice it Tests', () => {
    test('Place order with Klarna Slice it', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
        });
    });

    test('Place order with Klar<PERSON> Slice it (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
            disabledSteps: [OrderSteps.StorefrontLogin],
        });
    });

    test('Refund order with Klar<PERSON> Slice it (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
        });
        await refundService.executeSteps();
    });

    test('Partial refund order with Klarna Slice it (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Klarna Slice it (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
        });
        await plazaRefundService.executeSteps();
    });

    test('Place order with Klarna Slice it (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('klarnain', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KLARNA_SLICEIT),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Klarna Slice it (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('klarnain', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KLARNA_SLICEIT),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KLARNA_SLICEIT,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
