import { PaymentMethod } from '@woo/services/payments';
import { OrderSteps } from '@magento2/services/storefront';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';

export default class BelfiusPaymentMethod extends PaymentMethod {
    /**
     * Executes the steps to handle unsuccessful order placements.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);

        await this.submitStatus(this.options.responseStatus);

        await expect(wooPlugin.page).toHaveURL(/\/checkout\/\?bck_err=.+/);
        console.log('Order succeeded as expected');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        console.log('Submitting status...');
        await this.submitStatus();
    }

    /**
     * Submits the payment status.
     */
    private async submitStatus(responseStatus: string = '190'): Promise<void> {
        await wooPlugin.page.selectOption(wooPlugin.getSelector('storefront.checkout.selectStatus'), responseStatus);
        await wooPlugin.page.click(wooPlugin.getSelector('storefront.checkout.submitStatus'));
    }
}
