import { PaymentMethods } from '@/utils/paymentMethods';
import { test } from '@magento2/fixtures/BaseTest';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage, AdminConfigPage } from '@magento2/pages/admin';

test.describe('Setup Preconditions tests', () => {
    test.skip(); // Skipping this test suite for now - We can run it separately when needed

    test('Payment method configuration', async () => {
        await new AdminBuckarooConfigPage().setConfigsForAllPaymentMethods([
            {
                key: magentoPlugin.getSelector('admin.config.enablePaymentMethodDropdown'),
                value: 'Test',
                method: 'selectOption',
            },
            {
                key: '[name*="payment_fee"]:visible',
                value: '',
                method: 'fill',
            },
        ]);

        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.PAYBYBANK, [
            {
                key: magentoPlugin.getSelector('admin.config.enablePaymentMethodDropdown'),
                value: 'Test',
                method: 'selectOption',
            },
        ]);

        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.RIVERTY, [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFlow')(PaymentMethods.RIVERTY),
                value: 'Combined',
                method: 'selectOption',
            },
            {
                key: '[data-ui-id="select-groups-buckaroo-magento2-payment-section-groups-buckaroo-magento2-afterpay20-fields-customer-type-value"]',
                value: 'Both',
                method: 'selectOption',
            },
        ]);

        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.KLARNA_AUTHCAPT, [
            {
                key: 'select[id$="klarnakp_buckaroo_magento2_advanced_create_invoice_after_shipment"]',
                value: 'Yes',
                method: 'selectOption',
            },
        ]);

        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.GIFTCARDS, [
            {
                key: magentoPlugin.getSelector('admin.config.enablePaymentMethodDropdown'),
                value: 'Test',
                method: 'selectOption',
            },
            {
                key: '[data-ui-id="select-groups-buckaroo-magento2-payment-section-groups-buckaroo-magento2-giftcards-fields-allowed-giftcards-value"]',
                value: [PaymentMethods.VVVGIFTCARD, PaymentMethods.BOEKENBON, PaymentMethods.FASHION_CHEQUE],
                method: 'selectOption',
            },
        ]);

        await new AdminBuckarooConfigPage().setConfig(PaymentMethods.CREDITCARD_DEBITCARD, [
            {
                key: "[data-ui-id='select-groups-buckaroo-magento2-payment-section-groups-buckaroo-magento2-creditcard-fields-group-creditcards-value']",
                value: 'Grouped',
                method: 'selectOption',
            },
            {
                key: "[data-ui-id='select-groups-buckaroo-magento2-payment-section-groups-buckaroo-magento2-creditcard-fields-allowed-issuers-value']",
                value: ['visa', 'mastercard', 'amex'],
                method: 'selectOption',
            },
            {
                key: magentoPlugin.getSelector('admin.config.paymentFlow')(PaymentMethods.CREDITCARD_DEBITCARD),
                value: 'Combined',
                method: 'selectOption',
            },
        ]);
    });

    test('Add Free Shipping Method', async () => {
        await new AdminConfigPage().addFreeShippingMethod();
    });

    test('Enable Admin Account Sharing and extend Session Lifetime', async () => {
        await new AdminConfigPage().enableAdminAccountSharingAndSessionTime();
    });

    test('Allow backorders and Qty below 0', async () => {
        await new AdminConfigPage().allowBackOrders();
    });

    test('Set invoice handling "Create Invoice on Payment"', async () => {
        await new AdminConfigPage().updateInvoiceHandling('payment');
    });

    test('Set the time to CET', async () => {
        await new AdminConfigPage().setTimeZone('Europe/Tirane');
    });
});
