import { CheckoutPage } from '@woo/pages/storefront';
import { PaymentGatewayOptions, ProcessFlowService, Step } from 'core/services/workflow';
import { OrderExecuteStepsParams, OrderPlacementPages, OrderSteps } from '@woo/services/storefront/Order.types';
import OrderSuccessfulPage from '@woo/pages/storefront/OrderSuccessfulPage';
import { AuthApiService, CheckoutApiService, ProductApiService } from '@woo/services/api';
import wooPlugin from '@woo/WooPlugin';
import AdminOrderService from '@woo/services/admin/AdminOrder.service';

export default class OrderService<TPaymentGatewayOptions extends PaymentGatewayOptions> extends ProcessFlowService<
    OrderSteps,
    OrderPlacementPages,
    OrderExecuteStepsParams<TPaymentGatewayOptions>
> {
    protected adminOrderService: AdminOrderService;
    protected checkoutApiService: CheckoutApiService;

    constructor() {
        super();

        this.pages.checkoutPage = new CheckoutPage();
        this.pages.orderSuccessfulPage = new OrderSuccessfulPage();
        this.adminOrderService = new AdminOrderService();
        this.checkoutApiService = new CheckoutApiService();
    }

    /**
     * Handles what happens when a step is disabled
     * This method can be overridden in child classes to implement custom behavior
     * @param step The step that is disabled
     */
    protected async onStepDisabled(step: Step<OrderSteps>): Promise<void> {
        console.log(`Step "${step.name}" is disabled. Skipping.`);

        if (step.name === OrderSteps.StorefrontLogin) {
            await new AuthApiService().logout();
        }
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<OrderSteps>[] {
        return [
            {
                name: OrderSteps.StorefrontLogin,
                action: async () => {
                    await new AuthApiService().login();
                },
            },
            {
                name: OrderSteps.AddItemToCart,
                action: async () => {
                    await this.checkoutApiService.clearCart();
                    await new ProductApiService().addItemsToCart(this.options.products);
                },
            },
            {
                name: OrderSteps.GoToCheckout,
                action: async () => {
                    await this.pages.checkoutPage.navigateHere();
                },
            },
            {
                name: OrderSteps.SelectShippingMethod,
                action: async () => {
                    await this.pages.checkoutPage.fillFormForGuest();

                    await this.pages.checkoutPage.selectShippingMethod(this.options.shippingMethod);
                },
            },
            {
                name: OrderSteps.SelectPaymentMethod,
                action: async () => {
                    if (!this.options.paymentMethod) throw new Error('Payment method is required');

                    await this.pages.checkoutPage.selectPaymentMethod(this.options.paymentMethod);
                    await this.checkoutApiService.getCheckoutData();

                    if (this.options.paymentGatewayOptions?.paymentFee) {
                        await this.pages.checkoutPage.checkPaymentFee(this.options.paymentGatewayOptions.paymentFee);
                    }
                },
            },
            {
                name: OrderSteps.PlaceOrder,
                action: async () => {
                    await this.pages.checkoutPage.placeOrder();
                },
            },
            {
                name: OrderSteps.SuccessPage,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.pages.orderSuccessfulPage.waitForSuccessPage();
                },
            },
            {
                name: OrderSteps.AdminVerifyOrderStatus,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.adminOrderService.verifyOrder(wooPlugin.sharedData['orderNumber'], 'processing');
                },
            },
            {
                name: OrderSteps.BuckarooTransactionVerify,
                group: OrderSteps.AdminVerifyOrder,
                action: async () => {
                    await this.adminOrderService.verifyBuckarooTransaction(wooPlugin.sharedData['orderNumber']);
                },
            },
        ];
    }
}
