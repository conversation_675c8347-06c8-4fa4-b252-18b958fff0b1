import { expect, Locator } from '@playwright/test';
import { MoneyParser } from '@core/services/moneyParser';
import { isString } from 'lodash-es';
import { DotNotationKeys, SelectorKey } from '@core/context/TestContext.types';
import { TestContext } from '@core/context/TestContext';

export default class Helper<TSelectors extends Record<string, any> = Record<string, any>> {
    protected _moneyParser!: MoneyParser;
    protected context: TestContext;

    constructor(context: TestContext) {
        this.context = context;
    }

    /**
     * Retrieves a Locator instance from either a typed selector key or a Locator.
     * If a string key is provided, it uses context.getSelector to find the corresponding selector.
     */
    public resolveLocator(elementRef: DotNotationKeys<TSelectors> | Locator | string): Locator {
        if (isString(elementRef)) {
            return this.context.page.locator(this.context.getSelector(elementRef as SelectorKey, undefined, { throwOnMissing: false }) ?? elementRef);
        }

        return elementRef;
    }

    /**
     * Clicks a locator and waits for it to be visible.
     * @param elementRef
     * @param errorMessage - Error message if the locator is not visible.
     */
    public async clickAndExpectVisible(elementRef: SelectorKey | Locator, errorMessage?: string): Promise<void> {
        const locator = this.resolveLocator(elementRef);

        await expect(locator, errorMessage).toBeVisible({ timeout: 20000 });
        await locator.click();
    }

    /**
     * Waits for an element to be visible and then clicks it.
     *
     * You can specify the element using either:
     * - A typed key (string) that maps to a selector, or
     * - A Locator instance directly.
     *
     * @param elementRef - Either a typed key from SelectorsMapType[Stage] or a Playwright Locator.
     * @param errorMessage - The error message if the element is not visible within the timeout.
     * @param clickOptions
     */
    public async waitForVisibleAndClick(elementRef: SelectorKey | Locator, errorMessage: string, clickOptions?: any): Promise<void> {
        const locator = this.resolveLocator(elementRef);

        await locator.waitFor({ state: 'visible', timeout: 10000 }).catch(() => {
            throw new Error(errorMessage);
        });

        await locator.click(clickOptions);
    }

    /**
     * Retrieves and trims text content from a locator.
     * @param elementRef
     * @param options - Options for the extraction.
     * @param options.strict - If true, throws an error if text is not found.
     * @param options.errorMessage - The error message to throw if strict and text not found.
     * @returns The trimmed text content or null.
     */
    public async getText(
        elementRef: SelectorKey | Locator | string,
        options?: {
            strict?: boolean;
            errorMessage?: string;
        }
    ): Promise<string | null> {
        const locator = this.resolveLocator(elementRef).first();

        if (await locator.isVisible()) {
            const textContent = await locator.textContent();
            if (textContent) {
                return textContent.trim();
            }
        }

        if (options?.strict) {
            throw new Error(options.errorMessage || `Element with selector "${elementRef}" not found.`);
        }

        return null;
    }

    /**
     * Parses the amount string to extract the numeric value and optionally the currency symbol.
     * @returns The numeric value or an object containing the value and currency, or null if invalid.
     */
    public moneyParser(): MoneyParser {
        if (!this._moneyParser) {
            this._moneyParser = new MoneyParser(this.context);
        }
        return this._moneyParser;
    }

    /**
     * Clicks on a locator if it is visible.
     * @param locator - The locator to interact with.
     * @param missingMessage - The message to log if the locator is not visible.
     * @returns True if clicked, false otherwise.
     */
    public async clickIfVisible(locator: Locator, missingMessage?: string): Promise<boolean> {
        if (await locator.isVisible()) {
            await locator.click();
            return true;
        }

        if (missingMessage) {
            console.log(missingMessage);
        }
        return false;
    }

    /**
     * Executes a provided async function with retries, refreshing the page after the first failure.
     * @param action - The async function to execute.
     * @param options - Configuration options for the execution.
     * @param options.retries - Number of retry attempts (default: 3).
     * @param options.delay - Delay in milliseconds between retries (default: 1000).
     * @param options.errorMessage - Custom error message if all retries fail.
     * @param options.logMessage - Optional log message for each attempt.
     * @param options.refreshOnFail - Whether to refresh the page after the first failure (default: true).
     * @returns The result of the async action.
     * @throws An error if the action fails after all retries.
     */
    public async executeWithRetry<T>(
        action: () => Promise<T>,
        options?: {
            retries?: number;
            delay?: number;
            errorMessage?: string;
            logMessage?: string;
            refreshOnFail?: boolean;
        }
    ): Promise<T> {
        const retries = options?.retries ?? 3;
        const delay = options?.delay ?? 2000;
        const errorMessage = options?.errorMessage || 'Action failed after retries.';
        const logMessage = options?.logMessage || 'Executing action';
        const refreshOnFail = options?.refreshOnFail ?? true;

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                console.log(`${logMessage} (Attempt ${attempt}/${retries})`);
                return await action();
            } catch (error) {
                if (attempt < retries) {
                    if (refreshOnFail) {
                        console.warn(`Attempt ${attempt} failed. Refreshing the page...`);
                        await this.context.page.reload();
                    }
                    console.warn(`Attempt ${attempt} failed. Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    console.error(errorMessage);
                    throw error;
                }
            }
        }

        throw new Error(errorMessage);
    }

    /**
     * Waits for an element to reach the specified state ('visible' or 'hidden').
     */
    protected async waitForState(
        elementRef: SelectorKey | Locator | string,
        state: 'visible' | 'hidden',
        options?: {
            timeout?: number;
            errorMessage?: string;
        }
    ): Promise<void> {
        const locator = this.resolveLocator(elementRef);

        try {
            await locator.waitFor({ state, timeout: options?.timeout ?? 20000 });
        } catch (error) {
            console.error(error);
            const elementDescription = isString(elementRef) ? elementRef : 'a locator instance';
            const message = options?.errorMessage ?? `Element ${elementDescription} was not ${state} after ${options?.timeout ?? 20000}ms.`;
            throw new Error(message);
        }
    }

    /**
     * Waits for an element to become visible.
     */
    public async waitToBeVisible(
        elementRef: SelectorKey | Locator | string,
        options?: {
            timeout?: number;
            errorMessage?: string;
        }
    ): Promise<void> {
        await this.waitForState(elementRef, 'visible', options);
    }

    /**
     * Waits for an element to become hidden.
     */
    public async waitToBeHidden(
        elementRef: SelectorKey | Locator | string,
        options?: {
            timeout?: number;
            errorMessage?: string;
        }
    ): Promise<void> {
        await this.waitForState(elementRef, 'hidden', options);
    }

    /**
     * Expects a locator to be visible on the page.
     * @param elementRef - The selector key, Locator instance, or selector string to expect visibility.
     * @param options - Optional parameters for the expectation.
     * @param options.errorMessage - The error message to display if the locator is not visible.
     * @param options.timeout - Maximum time to wait for the element to become visible (in milliseconds).
     */
    public async expectToBeVisible(
        elementRef: SelectorKey | Locator | string,
        options?: {
            errorMessage?: string;
            timeout?: number;
        }
    ): Promise<void> {
        const locator = this.resolveLocator(elementRef);
        await expect(locator, options?.errorMessage).toBeVisible({ timeout: options?.timeout });
    }
}
