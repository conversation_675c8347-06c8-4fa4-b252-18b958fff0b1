import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { Gender } from '@core/selectors/customerData';
import { RefundExecuteStepsParams, RefundSteps } from '@magento2/services/admin';
import { AdminOrderDetailsPage } from '@magento2/pages/admin';
import { OrderSteps } from '@magento2/services/storefront/Order.types';
import { PaymentMethods } from '@/utils/paymentMethods';

export default class KlarnaPaymentMethod extends PaymentMethod<RefundExecuteStepsParams> {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        await this.processKlarnaPayment('06 87654321', '123456');
        await this.clickBuyButton();
    }

    /*
     * Handles actions before extracting the backend transaction in the admin panel..
     */
    async beforeAdminExtractBckTransaction(): Promise<void> {
        if (this.serviceCode !== PaymentMethods.KLARNA_AUTHCAPT) return;

        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);

        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderStatus('Processing');
        await new AdminOrderDetailsPage().shipCurrentOrder();
        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderStatus('Complete');
        await this.processFlowService.pages.adminOrderDetailsPage.verifyOrderTotalPaid();
    }

    /**
     * Handles actions after navigating to the credit memo page.
     */
    async afterGoToCreditMemo(): Promise<void> {
        if (this.serviceCode !== PaymentMethods.KLARNA_AUTHCAPT) return;

        this.processFlowService.addDisabledStep(RefundSteps.ProcessRefund);
        this.processFlowService.addDisabledStep(RefundSteps.VerifyRefund);

        await (this.options?.partial
            ? (console.log('Processing partial refund'), this.processFlowService.pages.adminRefundPage.processPartialRefund('Complete'))
            : (console.log('Processing full refund'), this.processFlowService.pages.adminRefundPage.processFullyRefund()));

        await this.processFlowService.pages.adminRefundPage.verifyRefund(this.options?.partial ? 'Complete' : 'Closed');
    }

    /**
     * Clicks the buy button in the Klarna payment flow.
     */
    async clickBuyButton(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaPay.buyButton'));
    }

    /**
     * Toggles the "Billing Same as Shipping" checkbox in the Klarna payment flow.
     */
    async toggleBillingSameAsShipping(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaSliceIt.billingSameAsShippingCheckbox'));
    }

    /**
     * Processes the Klarna payment flow with the provided phone number and verification code.
     * @param phone - The phone number to use for Klarna Pay.
     * @param code - The verification code to enter.
     */
    protected async processKlarnaPayment(phone: string, code: string): Promise<void> {
        await this.enterKlarnaPayPhone(phone);
        await this.clickContinueKlarnaPay();
        await this.enterKlarnaPayCode(code);
    }

    /**
     * Enters the phone number in the Klarna payment flow.
     * @param phone - The phone number to enter.
     */
    protected async enterKlarnaPayPhone(phone: string): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaPay.phone'));
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaPay.phone'), phone);
    }

    /**
     * Clicks the continue button in the Klarna payment flow.
     */
    protected async clickContinueKlarnaPay(): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaPay.continue'));
    }

    /**
     * Enters the verification code in the Klarna payment flow.
     * @param code - The verification code to enter.
     */
    protected async enterKlarnaPayCode(code: string): Promise<void> {
        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.klarnaPay.enterCode'));
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.klarnaPay.enterCode'), code);
    }

    /**
     * Selects the customer's gender in the checkout form.
     * @param gender - The gender to select.
     */
    protected async selectCustomerGender(gender: Gender): Promise<void> {
        await magentoPlugin.page.selectOption(magentoPlugin.getSelector('storefront.checkout.selectGender'), {
            value: gender,
        });
        console.log(`Selected Gender: ${gender}`);
    }
}
