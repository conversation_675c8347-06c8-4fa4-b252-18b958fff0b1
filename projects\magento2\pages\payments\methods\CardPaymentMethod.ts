import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { PaymentGatewayOptions } from 'core/services/workflow';

export default abstract class CardPaymentMethod<TOptions = PaymentGatewayOptions> extends PaymentMethod<TOptions> {
    /**
     * Enters the card information during checkout.
     */
    async enterCardInfo(): Promise<void> {
        const cardInfo = this.getCardDetails();

        await this.fillCardDetails(cardInfo);
        await this.fillCardCVVIfNeeded(cardInfo.cvv);

        await magentoPlugin.page.click(magentoPlugin.getSelector('paymentMethods.creditCards.clickAwayCart'));
    }

    /**
     * Provides the card details to be filled.
     * Must be implemented by subclasses.
     */
    protected abstract getCardDetails(): Record<string, any>;

    /**
     * Fills the main card details: name, number, expiration month, and year.
     * @param cardInfo - The card details object containing name, number, expMonth, and expYear.
     */
    protected async fillCardDetails(cardInfo: Record<string, any>): Promise<void> {
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.creditCards.nameOnCard'), cardInfo.name);
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.creditCards.cardNumber'), cardInfo.number);
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.creditCards.cardExpMonth'), cardInfo.expMonth);
        await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.creditCards.cardExpYear'), cardInfo.expYear);
    }

    /**
     * Fills the CVV field if it exists and CVV is provided.
     * @param cvv - The CVV code for the card.
     */
    protected async fillCardCVVIfNeeded(cvv?: string): Promise<void> {
        const cvvFieldExists = await magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.creditCards.cardCVV')).isVisible();
        if (cvvFieldExists && cvv) {
            await magentoPlugin.page.fill(magentoPlugin.getSelector('paymentMethods.creditCards.cardCVV'), cvv);
        }
    }
}
