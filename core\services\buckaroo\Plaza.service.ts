import { env } from '@/utils/env';
import { isEmpty } from 'lodash-es';
import { globalContext } from '@core/context/TestContext';

export default class PlazaService {
    /**
     * Navigates to the Transaction Detail page.
     * @param bckTransactionKey - Transaction Key.
     */
    public async navigateToTransaction(bckTransactionKey: string): Promise<void> {
        return await this.navigateToAdminPage(`/Transaction/Transactions/Details?transactionKey=${bckTransactionKey}`);
    }

    /**
     * Navigates to the specified Plaza page.
     * @param path - The path to navigate to, appended to PLAZA_BASE_URL.
     * @throws Will throw an error if PLAZA_BASE_URL is not set.
     */
    public async navigateToAdminPage(path: string): Promise<void> {
        const plazaBaseUrl = env('PLAZA_BASE_URL');

        if (isEmpty(plazaBaseUrl)) {
            throw new Error('PLAZA_BASE_URL environment variable is not set.');
        }

        await globalContext.page.goto(`${plazaBaseUrl}${path}`, {
            timeout: 10000,
            waitUntil: 'load',
        });
    }

    /**
     * Retrieves required admin credentials from environment variables.
     */
    public getPlazaCredentials(): { baseUrl: string; username: string; password: string } {
        const plazaBaseUrl = env('PLAZA_BASE_URL');
        const plazaUsername = env('PLAZA_USER');
        const plazaPassword = env('PLAZA_PASSWORD');

        if (!plazaBaseUrl || !plazaUsername || !plazaPassword) {
            throw new Error('ADMIN_BASE_URL, ADMIN_USER, and ADMIN_PASSWORD must be set in environment variables.');
        }

        return { baseUrl: plazaBaseUrl, username: plazaUsername, password: plazaPassword };
    }

    /**
     * Navigates to the Website settings page.
     */
    public async navigateToWebsiteSettings(): Promise<void> {
        return await this.navigateToAdminPage(`/Configuration/Website/Index/`);
    }
}
