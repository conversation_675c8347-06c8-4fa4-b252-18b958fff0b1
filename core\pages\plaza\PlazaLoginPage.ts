import BasePage from '@core/pages/BasePage';
import { PlazaService } from '@core/services/buckaroo';
import { globalContext } from '@core/context/TestContext';

export default class PlazaLoginPage extends BasePage {
    protected plazaService: PlazaService;

    constructor() {
        super();
        this.plazaService = new PlazaService();
    }

    /**
     * Performs login to the Plaza panel.
     */
    async login(): Promise<void> {
        if (globalContext.sharedData['isLoggedInPlaza']) {
            console.log('Already logged in to Plaza.');
        } else {
            console.log('Logging in to Plaza...');
            await this.plazaService.navigateToAdminPage(`/admin`);
            await this.fillLoginForm();
            globalContext.sharedData['isLoggedInPlaza'] = true;
        }
    }

    /**
     * Fills the login form with provided credentials and submits it.
     */
    protected async fillLoginForm(): Promise<void> {
        const { username, password } = this.plazaService.getPlazaCredentials();
        await globalContext.page.fill(globalContext.getSelector('plaza.usernameInput'), username);
        await globalContext.page.fill(globalContext.getSelector('plaza.passwordInput'), password);
        await globalContext.helper.clickAndExpectVisible('plaza.loginButton', 'Plaza login button not found.');
        console.log('Clicked on the login button.');

        await globalContext.page.waitForURL(/.*Dashboard.*/, { timeout: 30000 });
        console.log('Logged in to Plaza.');
    }
}
