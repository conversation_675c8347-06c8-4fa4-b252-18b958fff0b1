import { AdminOrderDetailsPage } from '@woo/pages/admin';
import { PlazaRefundPlacementPages } from '@core/services/buckaroo';

export enum AdminPlazaRefundSteps {
    AdminLogin = 'adminLogin',
    GoToOrder = 'goToOrder',
    VerifyRefundOnAdmin = 'verifyRefundOnAdmin',
}

export type AdminPlazaRefundPlacementPages = PlazaRefundPlacementPages & {
    adminOrderDetailsPage: AdminOrderDetailsPage;
};
