import { Step } from '@core/services/workflow/ProcessFlow.types';
import { PlazaRefundService, PlazaRefundSteps as BasePlazaRefundSteps } from '@core/services/buckaroo';
import { AdminPlazaRefundSteps } from '@magento2/services/admin/AdminPlazaRefund.types';
import { AdminLoginPage, AdminOrderDetailsPage, AdminRefundPage } from '@magento2/pages/admin';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminPlazaRefundPlacementPages } from '@magento2/services/admin/AdminPlazaRefund.types';

export default class AdminPlazaRefundService extends PlazaRefundService<
    BasePlazaRefundSteps | AdminPlazaRefundSteps,
    AdminPlazaRefundPlacementPages
> {
    constructor() {
        super();

        this.pages.adminLoginPage = new AdminLoginPage();
        this.pages.adminOrderDetailsPage = new AdminOrderDetailsPage();
        this.pages.adminRefundPage = new AdminRefundPage();
    }

    /**
     * Defines the sequence of steps for placing an order.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<BasePlazaRefundSteps | AdminPlazaRefundSteps>[] {
        return [
            ...super.defineSteps(),
            {
                name: AdminPlazaRefundSteps.AdminLogin,
                action: async () => {
                    await this.pages.adminLoginPage.login();
                },
            },
            {
                name: AdminPlazaRefundSteps.GoToOrder,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.setOrderNumber(magentoPlugin.sharedData['orderNumber']).findOrderByID();
                },
            },
            {
                name: AdminPlazaRefundSteps.VerifyRefundOnAdmin,
                action: async () => {
                    await this.pages.adminRefundPage.verifyPlazaRefundSuccess();
                },
            },
        ];
    }
}
