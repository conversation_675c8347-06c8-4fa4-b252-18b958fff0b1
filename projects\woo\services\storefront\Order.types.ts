import { Page } from '@playwright/test';
import { CheckoutPage, OrderSuccessfulPage } from '@woo/pages/storefront';
import { ExecuteStepsParams, PaymentGatewayOptions } from 'core/services/workflow';

export interface Product {
    id: string;
    quantity?: number;
    sizeOptionId?: string;
    colorOptionId?: string;
}

export enum OrderSteps {
    StorefrontLogin = 'storefrontLogin',
    AddItemToCart = 'addItemToCart',
    GoToCheckout = 'goToCheckout',
    SelectShippingMethod = 'selectShippingMethod',
    SelectPaymentMethod = 'selectPaymentMethod',
    PlaceOrder = 'placeOrder',
    SuccessPage = 'successPage',
    AdminVerifyOrder = 'adminVerifyOrder',
    AdminVerifyOrderStatus = 'adminVerifyOrderStatus',
    BuckarooTransactionVerify = 'buckarooTransactionVerify',
}

export type orderPlacementPages = {
    checkoutPage: typeof CheckoutPage;
    orderSuccessfulPage: typeof OrderSuccessfulPage;
};

export type OrderPlacementPages = { page: Page } & {
    [K in keyof orderPlacementPages]: InstanceType<orderPlacementPages[K]>;
};

export interface OrderExecuteStepsParams<TPaymentGatewayOptions extends PaymentGatewayOptions>
    extends ExecuteStepsParams<OrderSteps, TPaymentGatewayOptions> {
    paymentGatewayOptions?: TPaymentGatewayOptions;
    shippingMethod?: 'free' | 'flat';
    products?: Product[];
}

export interface OrderSummary {
    subtotal: number | null;
    discount?: number | null;
    buckarooPaidAmount?: number | null;
    alreadyPaid?: number | null;
    shippingFee: number | null;
    orderTotal: number | null;
    remainingAmount?: number | null;
    paidAmount?: number | null;
    buckarooFee?: number | null;
    totalTax?: number | null;
    currency: string | null;
}
