import { Step } from '@core/services/workflow/ProcessFlow.types';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminLoginPage, AdminOrderDetailsPage, AdminRefundPage } from '@magento2/pages/admin';
import { ProcessFlowService } from 'core/services/workflow';
import { RefundExecuteStepsParams, RefundPlacementPages, RefundSteps } from '@magento2/services/admin';

export default class AdminRefundService extends ProcessFlowService<RefundSteps, RefundPlacementPages, RefundExecuteStepsParams> {
    constructor() {
        super();

        this.pages.adminRefundPage = new AdminRefundPage();
        this.pages.adminLoginPage = new AdminLoginPage();
        this.pages.adminOrderDetailsPage = new AdminOrderDetailsPage();
    }

    /**
     * Checks if the user is inside the order details page.
     */
    protected async isInsideOrderDetailsPage(): Promise<boolean> {
        return await this.pages.adminOrderDetailsPage.setOrderNumber(magentoPlugin.sharedData['orderNumber']).isInsideDetailsPage();
    }

    /**
     * Defines the sequence of steps for refunding from Plaza.
     *
     * @returns {Step[]}
     */
    protected defineSteps(): Step<RefundSteps>[] {
        return [
            {
                name: RefundSteps.StorefrontLogin,
                action: async () => {
                    if (await this.isInsideOrderDetailsPage()) {
                        return;
                    }

                    await this.pages.adminLoginPage.login();
                },
            },
            {
                name: RefundSteps.GoToOrder,
                action: async () => {
                    if (await this.isInsideOrderDetailsPage()) {
                        return;
                    }

                    await this.pages.adminOrderDetailsPage.setOrderNumber(magentoPlugin.sharedData['orderNumber']).findOrderByID();
                },
            },
            {
                name: RefundSteps.GoToCreditMemo,
                action: async () => {
                    await this.pages.adminOrderDetailsPage.navigateToCreditMemo();
                },
            },
            {
                name: RefundSteps.ProcessRefund,
                action: async () => {
                    if (this.options?.partial) {
                        await this.pages.adminRefundPage.processPartialRefund('Processing');
                    } else {
                        await this.pages.adminRefundPage.processFullyRefund();
                    }
                },
            },
            {
                name: RefundSteps.VerifyRefund,
                action: async () => {
                    await this.pages.adminRefundPage.verifyRefund(this.options?.partial ? 'Processing' : 'Closed');
                },
            },
        ];
    }
}
