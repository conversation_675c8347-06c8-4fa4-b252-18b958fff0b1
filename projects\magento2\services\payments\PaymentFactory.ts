import { PaymentMethodCode, PaymentMethods } from '@/utils/paymentMethods';
import * as PaymentMethodsMap from '@magento2/pages/payments';
import { PaymentFactory as BasePaymentFactory, PaymentMethodConstructor } from '@core/services/payments';

export default class PaymentFactory extends BasePaymentFactory {
    /**
     * Registry mapping payment method keys to their respective classes.
     */
    public static readonly paymentMethodRegistry: Partial<Record<PaymentMethodCode, PaymentMethodConstructor<any>>> = {
        [PaymentMethods.ALIPAY]: PaymentMethodsMap.AlipayPaymentMethod,
        [PaymentMethods.BILLINK]: PaymentMethodsMap.BillinkPaymentMethod,
        [PaymentMethods.CREDITCARD_DEBITCARD]: PaymentMethodsMap.CreditCardPaymentMethod,
        [PaymentMethods.GIFTCARDS]: PaymentMethodsMap.GiftCardPaymentMethod,

        [PaymentMethods.BOEKENBON]: PaymentMethodsMap.GiftCardPaymentMethod,
        [PaymentMethods.VVVGIFTCARD]: PaymentMethodsMap.GiftCardPaymentMethod,
        [PaymentMethods.FASHION_CHEQUE]: PaymentMethodsMap.GiftCardPaymentMethod,

        [PaymentMethods.IDEAL]: PaymentMethodsMap.IdealPaymentMethod,
        [PaymentMethods.IN3]: PaymentMethodsMap.In3PaymentMethod,
        [PaymentMethods.KLARNA_AUTHCAPT]: PaymentMethodsMap.KlarnaPaymentMethod,
        [PaymentMethods.KLARNA]: PaymentMethodsMap.KlarnaPayPaymentMethod,
        [PaymentMethods.KLARNA_SLICEIT]: PaymentMethodsMap.KlarnaSliceItPaymentMethod,
        [PaymentMethods.MULTIBANCO]: PaymentMethodsMap.MultibancoPaymentMethod,
        [PaymentMethods.PAYBYBANK]: PaymentMethodsMap.PayByBankPaymentMethod,
        [PaymentMethods.PAYPAL]: PaymentMethodsMap.PaypalPaymentMethod,
        [PaymentMethods.MBWAY]: PaymentMethodsMap.MbwayPaymentMethod,
        [PaymentMethods.KBC]: PaymentMethodsMap.KbcPaymentMethod,
        [PaymentMethods.BELFIUS]: PaymentMethodsMap.BelfiusPaymentMethod,
        [PaymentMethods.RIVERTY]: PaymentMethodsMap.RivertyPaymentMethod,
        [PaymentMethods.BANCONTACT]: PaymentMethodsMap.CreditCardPaymentMethod,
        [PaymentMethods.WECHATPAY]: PaymentMethodsMap.WeChatPayPaymentMethod,
        [PaymentMethods.TRUSTLY]: PaymentMethodsMap.TrustlyPaymentMethod,
        [PaymentMethods.BANK_TRANSFER]: PaymentMethodsMap.TransferPaymentMethod,
        [PaymentMethods.SEPA]: PaymentMethodsMap.SepaPaymentMethod,
        [PaymentMethods.BLIK]: PaymentMethodsMap.BlikPaymentMethod,
        [PaymentMethods.PRZELEWY24]: PaymentMethodsMap.Przelewy24PaymentMethod,
        [PaymentMethods.PAYPEREMAIL]: PaymentMethodsMap.PayPerEmailPaymentMethod,
    };
}
