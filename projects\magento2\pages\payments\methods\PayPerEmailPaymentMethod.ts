import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { ask } from '@/utils/ask';
import { env } from '@/utils/env';

export default class PayPerEmailPaymentMethod extends PaymentMethod {
    /**
     * Handles operations that should be performed after selecting the payment method.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        const salutationInput = magentoPlugin.helper.resolveLocator('paymentMethods.payPerEmail.salutationInput');
        await salutationInput.selectOption('1');
        await magentoPlugin.helper.resolveLocator('paymentMethods.payPerEmail.emailInput').fill(env('PERSONAL_EMAIL'));
        await salutationInput.press('Tab');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        const answer = (await ask("What's the Payment URL: ")) as string;
        console.log('Navigating to the Payment URL...');
        await magentoPlugin.page.goto(answer);
        await magentoPlugin.page.waitForURL('**/pay.buckaroo.nl/**', { timeout: 5000 });
        console.log('Navigated to the Payment URL');

        await magentoPlugin.page.click('div.payment-method[role="link"]:has(input#iDEAL)');
        console.log('Selected iDEAL');

        await magentoPlugin.helper.resolveLocator('storefront.checkout.selectStatus').selectOption('190');
        console.log('Selected status');

        await magentoPlugin.helper.resolveLocator('storefront.checkout.submitStatus').click();
        console.log('Submitted status');
    }
}
