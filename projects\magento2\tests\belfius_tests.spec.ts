import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import magentoPlugin from '@magento2/MagentoPlugin';
import { OrderSteps } from '@magento2/services/storefront';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('Belfius Tests', () => {
    test('Place order with Belfius', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
        });
    });

    test('Place order with <PERSON><PERSON><PERSON> (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with Belfius (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Belfius (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Belfius (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Belfius (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('belfius', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.BELFIUS),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Belfius (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('belfius', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.BELFIUS),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with Belfius (failed)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '490' },
        });
    });

    test('Place order with Belfius (Canceled by user)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.BELFIUS,
            shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });
});
