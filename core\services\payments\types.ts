import { ExecuteStepsParams, PaymentGatewayOptions, ProcessFlowService } from '@core/services/workflow';
import BasePage from '@core/pages/BasePage';
import { PaymentMethod } from '@core/services/payments';

export interface PaymentMethodParams<
    TOptions = PaymentGatewayOptions,
    TParams extends ExecuteStepsParams<any, TOptions> = ExecuteStepsParams<any, TOptions>,
> {
    checkoutPage: BasePage;
    options: TParams;
}

export interface PaymentMethodConstructor<TOptions extends ExecuteStepsParams<any> = ExecuteStepsParams<any>> {
    new (processFlowService: ProcessFlowService): PaymentMethod<TOptions>;
}
