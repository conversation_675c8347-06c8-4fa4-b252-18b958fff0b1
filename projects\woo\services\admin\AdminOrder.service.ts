import { OrderApiService } from '@woo/services/api';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';
import { TransactionStatus } from '@magento2/pages/admin/types';
import { TransactionStatusService } from '@core/services/buckaroo';
import { get, isNil } from 'lodash-es';
import { TransactionResponse } from '@buckaroo/buckaroo_sdk';

export default class AdminOrderService {
    protected orderData!: any;

    /**
     * Retrieves the order details for the specified order number.
     *
     * @param orderNumber - The order number to check.
     * @param expectedStatus
     * @returns The order status.
     */
    async verifyOrder(orderNumber: string, expectedStatus: string = 'Processing'): Promise<any> {
        this.orderData = this.orderData ?? (await new OrderApiService().getOrderStatus(orderNumber));

        expect(this.orderData.status).toBe(expectedStatus);
        expect(this.orderData.amounts?.total).toEqual(wooPlugin.sharedData['orderSummary']['totals']['total']);
    }

    /**
     * Verifies the status of a Buckaroo transaction.
     *
     * @param orderNumber - The order number to check.
     * @param expectedStatus - The expected transaction status.
     * @returns The transaction status.
     */
    async verifyBuckarooTransaction(orderNumber: string, expectedStatus: TransactionStatus = 'success'): Promise<any> {
        console.log('Verifying transaction using Node SDK...');
        this.orderData = this.orderData ?? (await new OrderApiService().getOrderStatus(orderNumber));

        console.log(`Extracted Transaction ID: ${this.orderData.transaction_id}`);
        wooPlugin.sharedData['bckTransactionKey'] = this.orderData.transaction_id;

        const transactionService = new TransactionStatusService(this.orderData.transaction_id);
        const transactionResponse = await transactionService.status();

        const relatedTransactionKey = get(transactionResponse.data, 'relatedTransactions.0.relatedTransactionKey');

        const getStatusCheck = (response: TransactionResponse) => {
            switch (expectedStatus) {
                case 'success':
                    return response.isSuccess();
                case 'rejected':
                    return response.isRejected();
                case 'canceled':
                    return response.isCanceled();
                case 'pendingProcessing':
                    return response.isPendingProcessing();
                case 'failed':
                    return response.isFailed();
                default:
                    throw new Error(`Unsupported transaction status: ${expectedStatus}`);
            }
        };

        if (!isNil(relatedTransactionKey)) {
            const relatedTransactionService = new TransactionStatusService(relatedTransactionKey as string);
            const relatedTransactionResponse = await relatedTransactionService.status();
            console.log(`Extracted Related Transaction ID: ${relatedTransactionResponse.getTransactionKey()}`);

            expect(getStatusCheck(relatedTransactionResponse)).toBe(true);
            expect(relatedTransactionResponse.getAmountDebit()).toBe(this.orderData.amounts?.total);
            return;
        }

        expect(getStatusCheck(transactionResponse)).toBe(true);
        expect(transactionResponse.getAmountDebit()).toBe(parseFloat(this.orderData.amounts?.total));

        console.log(`Verified transaction status '${expectedStatus}'`);
    }
}
