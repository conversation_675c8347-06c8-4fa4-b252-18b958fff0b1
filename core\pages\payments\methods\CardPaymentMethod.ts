import { PaymentMethod } from '@core/services/payments';
import { PaymentGatewayOptions } from '@core/services/workflow';
import { globalContext } from '@core/context/TestContext';

export default abstract class CardPaymentMethod<TOptions = PaymentGatewayOptions> extends PaymentMethod<TOptions> {
    /**
     * Enters the card information during checkout.
     */
    async enterCardInfo(): Promise<void> {
        const cardInfo = this.getCardDetails();

        await this.fillCardDetails(cardInfo);
        await this.fillCardCVVIfNeeded(cardInfo.cvv);

        await globalContext.page.click(globalContext.getSelector('payments.creditCards.clickAwayCart'));
    }

    /**
     * Provides the card details to be filled.
     * Must be implemented by subclasses.
     */
    protected abstract getCardDetails(): Record<string, any>;

    /**
     * Fills the main card details: name, number, expiration month, and year.
     * @param cardInfo - The card details object containing name, number, expMonth, and expYear.
     */
    protected async fillCardDetails(cardInfo: Record<string, any>): Promise<void> {
        await globalContext.page.fill(globalContext.getSelector('payments.creditCards.nameOnCard'), cardInfo.name);
        await globalContext.page.fill(globalContext.getSelector('payments.creditCards.cardNumber'), cardInfo.number);
        await globalContext.page.fill(globalContext.getSelector('payments.creditCards.cardExpMonth'), cardInfo.expMonth);
        await globalContext.page.fill(globalContext.getSelector('payments.creditCards.cardExpYear'), cardInfo.expYear);
    }

    /**
     * Fills the CVV field if it exists and CVV is provided.
     * @param cvv - The CVV code for the card.
     */
    protected async fillCardCVVIfNeeded(cvv?: string): Promise<void> {
        const cvvFieldExists = await globalContext.page.locator(globalContext.getSelector('payments.creditCards.cardCVV')).isVisible();
        if (cvvFieldExists && cvv) {
            await globalContext.page.fill(globalContext.getSelector('payments.creditCards.cardCVV'), cvv);
        }
    }
}
