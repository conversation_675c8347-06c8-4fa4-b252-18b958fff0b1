import { PaymentMethod } from '@woo/services/payments';
import wooPlugin from '@woo/WooPlugin';

export default class BillinkPaymentMethod extends PaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async beforePlaceOrder(): Promise<void> {
        await this.checkoutPage.fillDateOfBirth('01/01/1990');
        await wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.selectGender')).selectOption({ label: 'He/him' });
        await wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.conditionsCheckbox')).check();
    }
}
