import { CreditCardPaymentMethod as BaseCreditCardPaymentMethod } from '@core/pages/payments/methods';
import { OrderSteps } from '@woo/services/storefront';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';
import { globalContext } from '@core/context/TestContext';

export default class CreditCardPaymentMethod extends BaseCreditCardPaymentMethod {
    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        const cardInfo = this.getCardDetails();

        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.creditCards.nameOnCard'), cardInfo.name);
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.creditCards.cardNumber'), cardInfo.number);
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.creditCards.cardExpMonth'), cardInfo.expMonth);
        await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.creditCards.cardExpYear'), cardInfo.expYear);
        if (cardInfo.cvv) {
            await wooPlugin.page.fill(wooPlugin.getSelector('paymentMethods.creditCards.cardCVV'), cardInfo.cvv);
        }

        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.creditCards.clickAwayCart'));
        await wooPlugin.page.click(wooPlugin.getSelector('paymentMethods.creditCards.payNowBtn'));

        // Check if ccPaymentErrorMessage is visible → if so, skip
        const errorVisible = await wooPlugin.page.locator(wooPlugin.getSelector('storefront.checkout.ccPaymentErrorMessage')).isVisible();

        if (errorVisible) {
            console.log('Payment error message is visible — skipping submitPaymentStatus.');
            return;
        }

        await this.submitStatus();
    }

    async submitStatus(status: any = 'Y'): Promise<void> {
        await globalContext.page.selectOption(globalContext.getSelector('paymentMethods.creditCards.paymentStatusDropdown'), status);
        await globalContext.helper.waitForVisibleAndClick('storefront.checkout.submitStatus', 'Submitting status.');
    }

    /**
     * Handles actions after placing the order with a negative scenario.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);

        const responseStatus = this.options.responseStatus ?? '791';

        await this.submitStatus(responseStatus);

        await expect(wooPlugin.page.locator('h1.entry-title')).toHaveText('Checkout'); //tp for woo because no error displays currently

        console.log('Order failed as expected');
    }
}
