import magentoPlugin from '@magento2/MagentoPlugin';
import moment, { Moment } from 'moment';
import { TransactionFilter, TransactionInfo } from '@magento2/services/admin/AdminOrderHistory.types';
import { chain, every, first, keys, map, times } from 'lodash-es';

export default class AdminOrderHistoryService {
    private readonly selectors = {
        date: '.note-list-date',
        time: '.note-list-time',
        status: '.note-list-status',
        comment: '.note-list-comment',
        bckTransactionKey: 'a[href*="transactionKey"]',
    };

    /**
     * Fetches all transactions from the admin order notes.
     * @returns A promise that resolves to an array of TransactionInfo objects.
     */
    public async fetchAllTransactions(): Promise<TransactionInfo[]> {
        try {
            const items = magentoPlugin.page.locator(magentoPlugin.getSelector('admin.orderDetailed.notes'));

            const transactions = await Promise.all(times(await items.count(), i => this.parseTransaction(items.nth(i))));

            return chain(transactions)
                .compact() // Remove undefined/null results
                .sortBy(t => -(t.datetime?.valueOf() || 0)) // Sort descending by datetime
                .value();
        } catch (error) {
            console.error('Error fetching all transactions:', error);
            return [];
        }
    }

    /**
     * Parses a single transaction item to extract transaction details.
     * @param item - The locator for the transaction item.
     * @returns A promise that resolves to a TransactionInfo object or undefined if parsing fails.
     */
    private async parseTransaction(item: any): Promise<TransactionInfo | undefined> {
        try {
            const [date, time, status, comment, transactionKey] = await Promise.all(
                map(this.selectors, selector => magentoPlugin.helper.getText(item.locator(selector)) as Promise<string>)
            );

            const lowerComment = comment?.toLowerCase() ?? '';

            return {
                date,
                time,
                status,
                comment,
                bckTransactionKey: transactionKey?.replace('-capture', ''),
                amount: comment ? await magentoPlugin.helper.moneyParser().parse(comment) : undefined,
                isRefund: lowerComment.includes('refunded'),
                isPayment: lowerComment.includes('paid') || lowerComment.includes('captured') || lowerComment.includes('transaction id:'),
                isCapture: lowerComment.includes('captured') && transactionKey.includes('-capture'),
                datetime: moment(`${date} ${time}`, 'MMM D, YYYY h:mm:ss A') as Moment,
            };
        } catch (error) {
            console.warn('Failed to parse transaction:', error);
            return undefined;
        }
    }

    /**
     * Fetches transactions based on an optional filter.
     * @param filter - Optional filter criteria to apply to the transactions.
     * @returns A promise that resolves to an array of filtered TransactionInfo objects.
     */
    public async fetchTransactions(filter?: TransactionFilter): Promise<TransactionInfo[]> {
        const transactions = await this.fetchAllTransactions();
        return filter ? transactions.filter(this.createFilterPredicate(filter)) : transactions;
    }

    /**
     * Creates a predicate function based on the provided filter to filter transactions.
     * @param filter - The filter criteria.
     * @returns A predicate function to be used with Array.filter.
     */
    private createFilterPredicate(filter: TransactionFilter): (t: TransactionInfo) => boolean {
        const predicateMap: Record<string, (t: TransactionInfo) => boolean> = {
            fromDate: t => Boolean(filter.fromDate && t.datetime?.isSameOrAfter(filter.fromDate)),
            toDate: t => Boolean(filter.toDate && t.datetime?.isSameOrBefore(filter.toDate)),
            isRefund: t => filter.isRefund != null && t.isRefund === filter.isRefund,
            isPayment: t => filter.isPayment != null && t.isPayment === filter.isPayment,
            isCapture: t => filter.isCapture != null && t.isCapture === filter.isCapture,
            bckTransactionKey: t => Boolean(filter.bckTransactionKey && t.bckTransactionKey === filter.bckTransactionKey),
            hasBckTransactionKey: t =>
                filter.hasBckTransactionKey != null
                    ? filter.hasBckTransactionKey
                        ? Boolean(t.bckTransactionKey)
                        : !Boolean(t.bckTransactionKey)
                    : true,
        };

        // Extract relevant predicates based on provided filter keys
        const predicates = keys(filter)
            .map(key => predicateMap[key])
            .filter(Boolean) as Array<(t: TransactionInfo) => boolean>;

        return t => every(predicates, predicate => predicate(t));
    }

    /**
     * Fetches all refund transactions based on an optional filter.
     * @param filter - Optional filter criteria to apply to refund transactions.
     * @returns A promise that resolves to an array of refund TransactionInfo objects.
     */
    public fetchRefundTransactions(filter?: TransactionFilter): Promise<TransactionInfo[]> {
        return this.fetchTransactions({ ...filter, isRefund: true });
    }

    /**
     * Fetches the latest refund transaction based on the provided filter.
     * Throws an error if no transaction is found and findOrFail is true.
     * @param filter - Filter criteria with an optional findOrFail flag.
     * @returns A promise that resolves to the latest TransactionInfo or undefined.
     * @throws Error if no transaction is found and findOrFail is true.
     */
    public async fetchLatestRefundTransaction(
        filter: TransactionFilter & {
            findOrFail: true;
        }
    ): Promise<TransactionInfo>;

    public async fetchLatestRefundTransaction(
        filter?: TransactionFilter & {
            findOrFail?: false;
        }
    ): Promise<TransactionInfo | undefined>;

    public async fetchLatestRefundTransaction(
        filter?: TransactionFilter & {
            findOrFail?: boolean;
        }
    ): Promise<TransactionInfo | undefined> {
        const transactions = await this.fetchRefundTransactions(filter);
        const transaction = transactions[0];
        if (!transaction && filter?.findOrFail) {
            throw new Error('No paid transaction found.');
        }

        return transaction;
    }

    /**
     * Fetches all payment transactions based on an optional filter.
     * @param filter - Optional filter criteria to apply to payment transactions.
     * @returns A promise that resolves to an array of payment TransactionInfo objects.
     */
    public fetchPaymentTransactions(filter?: TransactionFilter): Promise<TransactionInfo[]> {
        return this.fetchTransactions({ ...filter, isPayment: true, hasBckTransactionKey: true });
    }

    /**
     * Fetches the latest payment transaction based on the provided filter.
     * Throws an error if no transaction is found and findOrFail is true.
     * @param filter - Filter criteria with an optional findOrFail flag.
     * @returns A promise that resolves to the latest TransactionInfo or undefined.
     * @throws Error if no transaction is found and findOrFail is true.
     */
    public async fetchLatestPaymentTransaction(
        filter: TransactionFilter & {
            findOrFail: true;
        }
    ): Promise<TransactionInfo>;

    public async fetchLatestPaymentTransaction(
        filter?: TransactionFilter & {
            findOrFail?: false;
        }
    ): Promise<TransactionInfo | undefined>;

    public async fetchLatestPaymentTransaction(
        filter?: TransactionFilter & {
            findOrFail?: boolean;
        }
    ): Promise<TransactionInfo | undefined> {
        const transaction = first(await this.fetchPaymentTransactions(filter));
        if (!transaction && filter?.findOrFail) {
            throw new Error('No paid transaction found.');
        }

        return transaction;
    }
}
