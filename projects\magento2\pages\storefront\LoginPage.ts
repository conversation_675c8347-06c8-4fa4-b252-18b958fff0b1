import { expect } from '@playwright/test';
import { env } from '@/utils/env';
import magentoPlugin from '@magento2/MagentoPlugin';
import { StorefrontBasePage } from '@magento2/pages/storefront/index';

interface Credentials {
    baseUrl: string;
    username: string;
    password: string;
}

export default class LoginPage extends StorefrontBasePage {
    /**
     * Logs in the user with the provided credentials.
     */
    async login(): Promise<void> {
        const { baseUrl, username, password } = this.getCredentials();

        console.log('Signing in...');
        await this.navigateToLoginPage(baseUrl);
        await this.fillLoginForm(username, password);
        await expect(
            magentoPlugin.page,
            'The storefront page did not display "My Account" as the title. This suggests the login attempt failed'
        ).toHaveTitle('My Account');
        magentoPlugin.sharedData['isLoggedInStorefront'] = true;
    }

    /**
     * Retrieves and validates credentials from environment variables.
     * Throws an error if any required variable is missing.
     */
    protected getCredentials(): Credentials {
        const baseUrl = env('BASE_URL');
        const username = env('USER_EMAIL');
        const password = env('PASSWORD');

        if (!baseUrl || !username || !password) {
            throw new Error('BASE_URL, USER_EMAIL, and PASSWORD must be set in environment variables.');
        }

        return { baseUrl, username, password };
    }

    /**
     * Navigates to a specified URL and verifies the page title.
     */
    protected async navigateToLoginPage(baseUrl: string): Promise<void> {
        await magentoPlugin.page.goto(`${baseUrl}/customer/account/login/`);
        console.log(`Navigated to login page at ${baseUrl}`);
    }

    /**
     * Fills the login form and submits it.
     */
    protected async fillLoginForm(username: string, password: string): Promise<void> {
        await magentoPlugin.page.fill(magentoPlugin.getSelector('storefront.auth.usernameInput'), username);
        await magentoPlugin.page.fill(magentoPlugin.getSelector('storefront.auth.passwordInput'), password);
        await magentoPlugin.page.click(magentoPlugin.getSelector('storefront.auth.submitButton'));
        console.log('Login form submitted.');
    }
}
