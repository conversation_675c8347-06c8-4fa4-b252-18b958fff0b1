import { wooApiService } from '@woo/services/api/index';

/**
 * Service class handling all product-related API operations
 */
export default class OrderApiService {
    /**
     * Service class handling all order-related API operations
     */
    async getOrderStatus(orderNumber: string): Promise<any> {
        const response = await wooApiService().post('/actions/action/order_status', {
            order_id: orderNumber,
        });

        if (response.success) {
            return response.data;
        }

        throw new Error(`Failed to get order details: ${response.message}`);
    }
}
