import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';

test.describe('Riverty Tests', () => {
    test('Place order with Riverty', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'flat',
        });
    });

    test('Place order with <PERSON><PERSON> (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with Riverty (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'free',
        });

        await refundService.executeSteps();
    });

    test('Partial refund order with Riverty (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with Riverty (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'flat',
        });

        await plazaRefundService.executeSteps();
    });

    test('Place order with Riverty (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('afterpay20', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.RIVERTY),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Riverty (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('afterpay20', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.RIVERTY),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.RIVERTY,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });
});
