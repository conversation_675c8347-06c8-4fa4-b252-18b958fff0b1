import { test } from '@magento2/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import { OrderSteps } from '@magento2/services/storefront';
import magentoPlugin from '@magento2/MagentoPlugin';
import { AdminBuckarooConfigPage } from '@magento2/pages/admin';
import PlazaWebsiteSettingsPage from '@core/pages/plaza/PlazaWebsiteSettingsPage';
import { PushContentTypes } from '@/utils/pushConentTypes';
import { env } from '@/utils/env';

test.describe('KBC Tests', () => {
    test('Place order with KBC', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'flat',
        });
    });

    test('Place order with KBC (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Refund order with KBC (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'free',
        });
        await refundService.executeSteps();
    });

    test('Partial refund order with KBC (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'free',
            products: [
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.heroHoodie'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.m'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.gray'),
                },
                {
                    quantity: 20,
                    id: magentoPlugin.getSelector('productValues.id.breatheEasy'),
                    sizeOptionId: magentoPlugin.getSelector('productValues.sizes.s'),
                    colorOptionId: magentoPlugin.getSelector('productValues.colors.white'),
                },
            ],
        });

        await refundService.executeSteps({ partial: true });
    });

    test('Refund order with KBC (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'flat',
        });
        await plazaRefundService.executeSteps();
    });

    test('Place order with KBC (with extra fixed fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('kbc', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KBC),
                value: '15',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with KBC (with extra percentage fee)', async ({ orderService }) => {
        await new AdminBuckarooConfigPage().setConfig('kbc', [
            {
                key: magentoPlugin.getSelector('admin.config.paymentFee')(PaymentMethods.KBC),
                value: '15%',
                method: 'fill',
            },
        ]);

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with KBC (Rejected)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            paymentGatewayOptions: { responseStatus: '690' },
        });
    });

    test('Place order with KBC (Cancelled by user)', async ({ orderService }) => {
        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });

    test('Place order with KBC (Rejected) - with content type "soap"', async ({ orderService }) => {
        test.skip(env('STAGE') !== 'refactor', 'Skipping test because STAGE is not "refactor".');

        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.soap);

        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            paymentGatewayOptions: { responseStatus: '690' },
            disabledSteps: [OrderSteps.AdminVerifyOrder],
        });
    });

    test('Place order with KBC (failed) - with content type "json"', async ({ orderService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.json);

        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            paymentGatewayOptions: { responseStatus: '490' },
            disabledSteps: [OrderSteps.AdminVerifyOrder],
        });
    });

    test('Place order with KBC (failed) - with content type "httppost"', async ({ orderService }) => {
        await new PlazaWebsiteSettingsPage().selectPushContentType(PushContentTypes.httppost);

        magentoPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.KBC,
            paymentGatewayOptions: { responseStatus: '490' },
            disabledSteps: [OrderSteps.AdminVerifyOrder],
        });
    });
});
