import { GiftCardInfo, GiftCardOptions } from '@magento2/pages/payments/methods/types';
import magentoPlugin from '@magento2/MagentoPlugin';
import { expect } from '@magento2/fixtures/BaseTest';
import { IdealPaymentMethod } from '@magento2/pages/payments';
import * as console from 'node:console';
import { isNil } from 'lodash-es';
import CardPaymentMethod from '@magento2/pages/payments/methods/CardPaymentMethod';
import { ProcessFlowService } from 'core/services/workflow';

const DEFAULT_GIFTCARD_AMOUNT = 10;

const cards: Record<string, { name: string; cardInfo: GiftCardInfo }> = {
    boekenbon: { name: '<PERSON><PERSON><PERSON><PERSON>', cardInfo: { number: '0000000000000000001' } },
    vvvgiftcard: { name: 'VVV Giftcard', cardInfo: { number: '0000000000000000001' } },
    fashioncheque: { name: 'Fashion cheque', cardInfo: { number: '1000000010' } },
};

export default class GiftCardPaymentMethod extends CardPaymentMethod<GiftCardOptions> {
    protected idealPaymentMethod: IdealPaymentMethod;
    protected _isInline!: boolean;

    constructor(processFlowService: ProcessFlowService) {
        super(processFlowService);
        this.idealPaymentMethod = new IdealPaymentMethod(processFlowService);
    }

    /**
     * Formats a given number as a 4-digit string, representing a PIN for the gift card.
     */
    protected formatPin(input: number): string {
        // For PIN, multiply by 100 and pad to 4 digits without any prefix
        return this.formatValue(input, 4);
    }

    /**
     * Formats a given number by multiplying, converting to string, padding, and optionally adding a prefix.
     *
     * @param input - The number to format.
     * @param padLength - The total length of the padded number string.
     * @param prefix - An optional prefix to add before the formatted number.
     * @returns The formatted string.
     */
    protected formatValue(input: number, padLength: number, prefix: string = ''): string {
        if (input < 0 || input > 50) {
            throw new Error('Input must be between 0 and 50.');
        }
        const multiplied = input * 100;
        const padded = multiplied.toString().padStart(padLength, '0');
        return `${prefix}${padded}`;
    }

    /**
     * Formats a given number as a 4-digit string, representing a PIN for the gift card.
     */
    protected formatNumber(): string {
        const cardDetails = this.getCardDetails();
        if (this.getGiftCardName() == 'fashioncheque') {
            return this.formatValue(this.options.amount ?? 10, 9, '1');
        }

        return cardDetails.number;
    }

    protected getGiftCardAmount(): number {
        return this.options.amount ?? DEFAULT_GIFTCARD_AMOUNT;
    }

    protected getGiftCardName(): string {
        return this.options.card ?? this.serviceCode;
    }

    protected getCardDetails(): GiftCardInfo {
        return cards[this.getGiftCardName()].cardInfo;
    }

    protected async isInline(): Promise<boolean> {
        if (isNil(this._isInline)) {
            this._isInline = await magentoPlugin.page
                .locator(magentoPlugin.getSelector('paymentMethods.giftCards.giftCardsInput')(this.serviceCode))
                .first()
                .isVisible({ timeout: 3000 });
        }
        return this._isInline;
    }

    /**
     * Performs inline gift card payment steps after selecting the payment method.
     */
    protected async handleInlineGiftCardPayment(): Promise<void> {
        const giftcardAmount = this.getGiftCardAmount();
        const giftCardsSelectors = magentoPlugin.getSelector('paymentMethods.giftCards') as Record<any, any>;
        const orderSummarySelectors = magentoPlugin.getSelector('storefront.checkout.orderSummary') as Record<any, any>;

        await magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.giftCards.inlinePinInput')).fill(this.formatPin(giftcardAmount));
        await magentoPlugin.page.locator(magentoPlugin.getSelector('paymentMethods.giftCards.inlineCardNumberInput')).fill(this.formatNumber());

        await magentoPlugin.page.locator(giftCardsSelectors.submitButton(this.serviceCode)).click();

        if (magentoPlugin.sharedData['orderSummary']['orderTotal'] <= giftcardAmount) {
            await magentoPlugin.page.waitForURL(/.*\/onepage\/success\/.*/, { timeout: 30000 });
            return;
        }

        await this.confirmPartialPaymentAndProceed(giftCardsSelectors, orderSummarySelectors, giftcardAmount);
    }

    /**
     * Confirms success message after partial payment and proceeds to pay remaining amount.
     */
    protected async confirmPartialPaymentAndProceed(
        giftCardsSelectors: Record<any, any>,
        orderSummarySelectors: Record<any, any>,
        giftcardAmount: number
    ): Promise<void> {
        await expect(
            magentoPlugin.page.getByRole('heading', { name: giftCardsSelectors.successHeading }),
            'The success message for the partial payment was not displayed'
        ).toBeVisible({
            timeout: 20000,
        });
        await magentoPlugin.page.getByRole('button', { name: giftCardsSelectors.payRemainingAmountButton }).click();
        magentoPlugin.sharedData['orderSummary'] = await this.checkoutPage.getOrderSummary();

        await expect(
            magentoPlugin.page.locator(orderSummarySelectors.buckarooPaidLabel),
            `'Paid with' label in the Order Summary does not contain the expected text '${cards[this.getGiftCardName()]['name']}'`
        ).toContainText(orderSummarySelectors.getPaidWithText(cards[this.getGiftCardName()]['name']));
        await expect(
            magentoPlugin.page.locator(orderSummarySelectors.prices.buckarooPaidAmount),
            `Expected the 'Paid with' amount in the Order Summary to equal '${giftcardAmount * -1}', but it did not match.`
        ).toHaveText(magentoPlugin.helper.moneyParser().formatAmount(giftcardAmount * -1));

        const checkoutSelectors = magentoPlugin.getSelector('storefront.checkout') as Record<any, any>;
        await magentoPlugin.page.locator(checkoutSelectors.selectPaymentMethod('ideal')).click();

        // commented this out since `afterSelectPaymentMethod` doesn't exists on iDEAL
        // await this.idealPaymentMethod.afterSelectPaymentMethod();
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        if (await this.isInline()) {
            if (magentoPlugin.sharedData['orderSummary']['orderTotal'] <= this.getGiftCardAmount()) {
                return;
            }
            await this.idealPaymentMethod.afterPlaceOrder();
        } else {
            await this.selectGiftCardRedirect();
        }
    }

    /**
     * After selecting the payment method, handles inline or redirect flow.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        if (!(await this.isInline())) {
            // If not inline, do nothing here as redirect flow will be handled after order placement.
            return;
        }
        await this.handleInlineGiftCardPayment();
    }

    /**
     * Handles redirect gift card data entry and payment processing.
     */
    protected async selectGiftCardRedirect(): Promise<void> {
        console.log('Redirect - Giftcards redirecting on buckaroo checkout...');

        const giftcardOnBuckarooCheckout = magentoPlugin
            .getSelector('paymentMethods.giftCards.buckarooCheckoutMethod')
            .replace('$paymentMethod', this.getGiftCardName());

        await magentoPlugin.page.click(giftcardOnBuckarooCheckout);
        await this.enterGiftCardDataRedirect(this.getGiftCardName());

        const clickAwaySelector = magentoPlugin.getSelector('paymentMethods.creditCards.clickAwayCart');
        const payNowInlineSelector = magentoPlugin.getSelector('paymentMethods.giftCards.payNowInline');

        await magentoPlugin.page.click(clickAwaySelector);
        await magentoPlugin.page.click(payNowInlineSelector);

        await this.handleRedirectProcess();
    }

    /**
     * Handles the redirect process after entering gift card details.
     */
    protected async handleRedirectProcess(): Promise<void> {
        const redirectButton = magentoPlugin.page.locator(magentoPlugin.getSelector('plaza.proceedButton'));
        try {
            await magentoPlugin.helper.waitToBeVisible(redirectButton);
            if (await redirectButton.isVisible()) {
                await redirectButton.click();
                await magentoPlugin.page.getByRole('main').locator('div').filter({ hasText: 'iDEAL' }).nth(1).click();
                await magentoPlugin.page.waitForLoadState('networkidle');
                await this.idealPaymentMethod.afterPlaceOrder();
            }
        } catch (error) {
            console.error('Error:', error);
            await magentoPlugin.page.waitForURL(/.*\/onepage\/success\/.*/, { timeout: 30000 });
        }
    }

    /**
     * Fills gift card data (card number and pin) for redirect entry.
     */
    protected async enterGiftCardDataRedirect(giftcardName: string): Promise<void> {
        console.log("Entering 'Card number' & 'Pin' for redirect entry...");

        const cardNumberField = magentoPlugin.getSelector('paymentMethods.giftCards.nrRedirect').replace('$giftcardName', giftcardName);
        const securityCodeField = magentoPlugin.getSelector('paymentMethods.giftCards.securityCodeRedirect').replace('$giftcardName', giftcardName);

        await magentoPlugin.page.fill(cardNumberField, this.formatNumber());
        await magentoPlugin.page.fill(securityCodeField, this.formatPin(this.getGiftCardAmount()));
    }
}
