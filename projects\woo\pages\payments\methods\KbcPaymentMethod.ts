import { PaymentMethod } from '@woo/services/payments';
import { OrderSteps } from '@magento2/services/storefront';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';

export default class KbcPaymentMethod extends PaymentMethod {
    /**
     * Executes the steps to handle unsuccessful order placements.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);
        this.processFlowService.addDisabledStep(OrderSteps.GoToOrder);
        this.processFlowService.addDisabledStep(OrderSteps.AdminExtractBckTransaction);

        await this.submitStatus(this.options.responseStatus);

        await expect(wooPlugin.page).toHaveURL(/\/checkout\/\?bck_err=.+/);
        console.log('Order failed!');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        console.log('Submitting status...');
        // Use the specific responseStatus if provided (e.g., for 791 - Pending processing)
        // Otherwise default to 190 (Success)
        await this.submitStatus(this.options.responseStatus);
    }

    /**
     * Submits the payment status.
     * KBC status codes: 190 (Success), 490 (Failed), 690 (Rejected), 890 (Cancelled by user), 791 (Pending processing)
     */
    private async submitStatus(responseStatus: string = '190'): Promise<void> {
        await wooPlugin.page.selectOption(wooPlugin.getSelector('storefront.checkout.selectStatus'), responseStatus);
        await wooPlugin.page.click(wooPlugin.getSelector('storefront.checkout.submitStatus'));
    }
}
