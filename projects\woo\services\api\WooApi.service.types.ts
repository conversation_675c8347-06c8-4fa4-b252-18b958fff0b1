/**
 * HTTP methods supported by the API
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * Standard response structure from the API
 */
export interface Response {
    success: boolean;
    data?: any;
    message?: string;
}

/**
 * Generic type for request data
 */
export type RequestData = Record<string, any>;

/**
 * Request options for API calls
 */
export interface RequestOptions {
    endpoint: string;
    method: HttpMethod;
    data?: RequestData;
}
