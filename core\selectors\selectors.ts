export default {
    payments: {
        ideal: {
            selectStatus: 'select[name="sc"]',
            submitStatus: 'input[value="Submit status"]',
        },
    },
    plaza: {
        usernameInput: 'input[name="username"]',
        passwordInput: 'input[name="password"]',
        loginButton: '#okta-signin-submit',
        errorContainer: '#buckaroo-error-container',

        testScenario: {
            actionsButton: '.btn.btn-default.btn-sm.dropdown-toggle',
            testScenarioLink: '[title="Test scenarios"]',
            successMessage: '#testCaseSuccessMessage',
            findScenario: (scenario: string) => `#testCasePanelbar li:has(a:has-text("${scenario}"))`,
            performScenarioButton: 'input[type="button"][value="Perform scenario"]',
        },

        search: {
            filterButton: '#transaction_list > div > div.ibox-title > button',
            searchField: '#search',
            dateField: '#datetime1',
            transactionTypeInput:
                '#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(3) > div > span > span.selection > span > ul > li > input',
            searchButton: '#MainContent > div.row > div.col-fixed-300 > div > div.ibox-content > form > div:nth-child(12) > div > button',
            transactionRows: 'table tbody tr',
            datePicker: '.bootstrap-datetimepicker-widget td[data-day]',
        },

        refund: {
            actionsButton: '.btn.btn-default.btn-sm.dropdown-toggle',
            refundLink: 'a[title="Refund"]',
            refundAmountInput: '#refund_amount',
            executeRefundButton: 'button.btn.btn-default:has(span:has-text("Execute refund"))',
            successStatusCell: 'td:has(#statusIcon):has-text("Success (190)")',
            successMessage: '.action_success',
            dateAndCreadedByDetails: 'td:has-text("Created:") + td',
        },

        proceedButton: '#consumer-messages-redirect-button',
        savedSuccessMessage: '.clearfix.notification.success.show',
        selectWebsiteDropdown: '#selectWebsite',
        pushContentTypeBtn: '#PushContentType',
    },
};
