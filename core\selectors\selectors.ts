export default {
    payments: {
        ideal: {
            selectStatus: 'select[name="sc"]',
            submitStatus: 'input[value="Submit status"]',
        },
    },
    plaza: {
        usernameInput: 'input[name="username"]',
        passwordInput: 'input[name="password"]',
        loginButton: '#okta-signin-submit',
        errorContainer: '#buckaroo-error-container',

        testScenario: {
            actionsButton: '.btn.btn-default.btn-sm.dropdown-toggle',
            testScenarioLink: '[title="Test scenarios"]',
            successMessage: '#testCaseSuccessMessage',
            findScenario: (scenario: string) => `#testCasePanelbar li:has(a:has-text("${scenario}"))`,
            performScenarioButton: 'input[type="button"][value="Perform scenario"]',
        },

        refund: {
            actionsButton: '.btn.btn-default.btn-sm.dropdown-toggle',
            refundLink: 'a[title="Refund"]',
            refundAmountInput: '#refund_amount',
            executeRefundButton: 'button.btn.btn-default:has(span:has-text("Execute refund"))',
            successStatusCell: 'td:has(#statusIcon):has-text("Success (190)")',
            successMessage: '.action_success',
            dateAndCreadedByDetails: 'td:has-text("Created:") + td',
        },

        proceedButton: '#consumer-messages-redirect-button',
        savedSuccessMessage: '.clearfix.notification.success.show',
        selectWebsiteDropdown: '#selectWebsite',
        pushContentTypeBtn: '#PushContentType',
    },
};
