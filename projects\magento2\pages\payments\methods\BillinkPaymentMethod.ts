import { PaymentMethod } from '@magento2/services/payments';
import magentoPlugin from '@magento2/MagentoPlugin';
import { customerData } from '@core/selectors/customerData';

export default class BillinkPaymentMethod extends PaymentMethod {
    /**
     * Handles operations that should be performed after selecting the payment method.
     */
    async afterSelectPaymentMethod(): Promise<void> {
        const paymentFieldset = magentoPlugin.helper.resolveLocator('paymentMethods.billink.cocInput');
        const companyIsFilled = await paymentFieldset.isVisible();
        if (companyIsFilled) {
            this.fillB2BForm();
        } else {
            this.fillB2CForm();
        }
    }

    /**
     * Fills B2C form.
     */
    async fillB2CForm() {
        const genreInputLocator = magentoPlugin.helper.resolveLocator('storefront.checkout.selectGender');
        await genreInputLocator.focus();
        await genreInputLocator.selectOption(customerData.genre);

        const phoneInputLocator = await magentoPlugin.helper.resolveLocator('storefront.checkout.phoneInput');

        const isVisible = await phoneInputLocator.isVisible();
        if (isVisible) {
            await phoneInputLocator.focus();
            await phoneInputLocator.fill(customerData.phone);
        } else {
            console.log('Phone input field is not visible, skipping...');
        }

        const birthDateLocator = magentoPlugin.helper.resolveLocator('storefront.checkout.dateOfBirth');
        await birthDateLocator.focus();
        await birthDateLocator.fill(customerData.dob);
        await birthDateLocator.press('Tab');

        await magentoPlugin.page.click(magentoPlugin.getSelector('storefront.checkout.placeOrderButton'));

        console.log('Personal details filled');
    }

    /**
     * Fills B2B required fields if company is filled.
     */
    async fillB2BForm(): Promise<void> {
        const cocInputLocator = magentoPlugin.helper.resolveLocator('paymentMethods.billink.cocInput');
        await cocInputLocator.focus();
        await cocInputLocator.fill('04060983');

        const vatInputLocator = magentoPlugin.helper.resolveLocator('paymentMethods.billink.vatInput');
        await vatInputLocator.focus();
        await vatInputLocator.fill('NL808888614.B01');

        await magentoPlugin.page.click(magentoPlugin.getSelector('storefront.checkout.placeOrderButton'));

        console.log('Company details filled');
    }
}
